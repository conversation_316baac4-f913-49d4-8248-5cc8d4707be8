#!/usr/bin/env python3
"""
Improved MOSFET Parameter Fitting Script with Log-Space Capacitance Fitting

This script implements:
1. Log-space capacitance fitting with 20th order polynomials
2. Comprehensive fitting including IdVg, IdVd, BVDSS, and capacitances
3. Log-scale plotting for better visualization
4. Multi-stage optimization strategy
"""

import sys
from pathlib import Path
import numpy as np

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.mosfet_fitter.optimization.parameter_fitter import ParameterFitter
from src.mosfet_fitter.output_generation.report_generator import ReportGenerator


def run_improved_fitting():
    """Run improved parameter fitting with log-space capacitance and all characteristics."""
    
    print("🚀 Starting Improved MOSFET Parameter Fitting")
    print("   - Log-space capacitance fitting with 20th order polynomials")
    print("   - All characteristics: IdVg, IdVd, BVDSS, Cgs, Cds, Cgd")
    print("   - Log-scale plotting for better visualization")
    print("=" * 70)
    
    # Initialize components
    fitter = ParameterFitter("raw_data", "output/models/improved_fitted_model.lib")
    report_generator = ReportGenerator("output")
    
    print(f"📊 Loaded measurement data: {list(fitter.measurement_data.keys())}")
    
    # Check which characteristics have data
    available_chars = []
    for char_name, measurement in fitter.measurement_data.items():
        if measurement is not None and len(measurement.x_data) > 0:
            available_chars.append(char_name)
            print(f"   ✅ {char_name}: {len(measurement.x_data)} data points")
        else:
            print(f"   ❌ {char_name}: No data or empty")
    
    if not available_chars:
        print("❌ No measurement data available for fitting!")
        return False
    
    # Stage 1: Fit capacitance parameters with log-space approach
    print(f"\n🔧 Stage 1: Log-Space Capacitance Fitting (20th order)")
    print("-" * 50)
    
    # Get all capacitance parameters (21 coefficients × 3 capacitances = 63 parameters)
    all_fittable = fitter.model_template.get_all_fittable_parameters()
    capacitance_params = []
    
    for cap_type in ['Cgs', 'Cds', 'Cgd']:
        if cap_type in available_chars:
            # Start with lower order terms for initial fitting
            for order in range(10):  # Use first 10 orders for initial fitting
                param_name = f'param_{cap_type}_{order}'
                if param_name in all_fittable:
                    capacitance_params.append(param_name)
    
    print(f"   Fitting {len(capacitance_params)} capacitance parameters")
    
    if capacitance_params:
        cap_result = fitter.fit_parameters(capacitance_params)
        
        if cap_result.success:
            print(f"✅ Capacitance fitting successful!")
            print(f"   Final cost: {cap_result.final_cost:.6e}")
            print(f"   Iterations: {cap_result.iterations}")
            print(f"   Time: {cap_result.fitting_time:.2f} seconds")
            
            # Update parameters with fitted values
            for name, value in cap_result.fitted_parameters.items():
                if name in all_fittable:
                    all_fittable[name].initial_value = value
        else:
            print(f"❌ Capacitance fitting failed: {cap_result.convergence_info}")
            cap_result = None
    else:
        print("⚠️  No capacitance parameters to fit")
        cap_result = None
    
    # Stage 2: Fit MOSFET parameters for current characteristics
    print(f"\n🔧 Stage 2: MOSFET Parameter Fitting")
    print("-" * 40)
    
    mosfet_params = []
    if any(char in available_chars for char in ['IdVg', 'IdVd']):
        mosfet_params = [
            'Vto',      # Threshold voltage
            'Kp',       # Transconductance parameter
            'U0',       # Surface mobility
            'Gamma',    # Body effect parameter
            'Phi',      # Surface potential
            'Lambda',   # Channel length modulation
        ]
        mosfet_params = [p for p in mosfet_params if p in all_fittable]
    
    mosfet_result = None
    if mosfet_params:
        print(f"   Fitting {len(mosfet_params)} MOSFET parameters")
        mosfet_result = fitter.fit_parameters(mosfet_params)
        
        if mosfet_result.success:
            print(f"✅ MOSFET parameter fitting successful!")
            print(f"   Final cost: {mosfet_result.final_cost:.6e}")
            print(f"   Iterations: {mosfet_result.iterations}")
            print(f"   Time: {mosfet_result.fitting_time:.2f} seconds")
            
            # Update parameters
            for name, value in mosfet_result.fitted_parameters.items():
                if name in all_fittable:
                    all_fittable[name].initial_value = value
        else:
            print(f"❌ MOSFET parameter fitting failed: {mosfet_result.convergence_info}")
            mosfet_result = None
    else:
        print("⚠️  No MOSFET parameters to fit")
    
    # Stage 3: Fit diode parameters for breakdown characteristics
    print(f"\n🔧 Stage 3: Diode Parameter Fitting")
    print("-" * 40)
    
    diode_params = []
    if 'BVDSS' in available_chars:
        diode_params = [
            'DBD_Bv',   # Breakdown voltage
            'DBD_Is',   # Saturation current
            'DBD_N',    # Ideality factor
        ]
        diode_params = [p for p in diode_params if p in all_fittable]
    
    diode_result = None
    if diode_params:
        print(f"   Fitting {len(diode_params)} diode parameters")
        diode_result = fitter.fit_parameters(diode_params)
        
        if diode_result.success:
            print(f"✅ Diode parameter fitting successful!")
            print(f"   Final cost: {diode_result.final_cost:.6e}")
            print(f"   Iterations: {diode_result.iterations}")
            print(f"   Time: {diode_result.fitting_time:.2f} seconds")
            
            # Update parameters
            for name, value in diode_result.fitted_parameters.items():
                if name in all_fittable:
                    all_fittable[name].initial_value = value
        else:
            print(f"❌ Diode parameter fitting failed: {diode_result.convergence_info}")
            diode_result = None
    else:
        print("⚠️  No diode parameters to fit")
    
    # Stage 4: Fine-tuning with key parameters from all categories
    print(f"\n🔧 Stage 4: Fine-Tuning All Parameters")
    print("-" * 40)
    
    # Select key parameters for fine-tuning
    fine_tune_params = []
    
    # Key capacitance parameters (constant and linear terms)
    for cap_type in ['Cgs', 'Cds', 'Cgd']:
        if cap_type in available_chars:
            for order in [0, 1, 2]:  # Constant, linear, quadratic
                param_name = f'param_{cap_type}_{order}'
                if param_name in all_fittable:
                    fine_tune_params.append(param_name)
    
    # Key MOSFET parameters
    key_mosfet = ['Vto', 'Kp']
    fine_tune_params.extend([p for p in key_mosfet if p in all_fittable])
    
    # Key diode parameter
    if 'DBD_Bv' in all_fittable:
        fine_tune_params.append('DBD_Bv')
    
    final_result = None
    if fine_tune_params:
        print(f"   Fine-tuning {len(fine_tune_params)} key parameters")
        final_result = fitter.fit_parameters(fine_tune_params)
        
        if final_result.success:
            print(f"✅ Fine-tuning successful!")
            print(f"   Final cost: {final_result.final_cost:.6e}")
            print(f"   Iterations: {final_result.iterations}")
            print(f"   Time: {final_result.fitting_time:.2f} seconds")
        else:
            print(f"⚠️  Fine-tuning failed, using previous results")
    
    # Combine all fitted parameters
    all_fitted_params = {}
    total_iterations = 0
    total_time = 0.0
    
    for result in [cap_result, mosfet_result, diode_result, final_result]:
        if result and result.success:
            all_fitted_params.update(result.fitted_parameters)
            total_iterations += result.iterations
            total_time += result.fitting_time
    
    if not all_fitted_params:
        print("❌ No successful fitting results!")
        return False
    
    # Create comprehensive result
    from src.mosfet_fitter.optimization.parameter_fitter import FittingResult
    
    # Get initial parameters
    initial_params = {}
    for name in all_fitted_params.keys():
        if name in all_fittable:
            initial_params[name] = all_fittable[name].initial_value
    
    comprehensive_result = FittingResult(
        success=True,
        fitted_parameters=all_fitted_params,
        initial_parameters=initial_params,
        final_cost=final_result.final_cost if final_result else (diode_result.final_cost if diode_result else (mosfet_result.final_cost if mosfet_result else cap_result.final_cost)),
        iterations=total_iterations,
        convergence_info=final_result.convergence_info if final_result else {"message": "Multi-stage fitting completed"},
        fitting_time=total_time
    )
    
    # Generate results
    print(f"\n📊 Generating Comprehensive Results")
    print("-" * 40)
    
    # Save fitted model
    fitter.save_fitted_model(comprehensive_result, "output/models/improved_fitted_model.lib")
    print("✅ Improved fitted model saved")
    
    # Generate comparison data and plots
    comparisons = report_generator.generate_comparison_data(fitter, comprehensive_result)
    plot_path = report_generator.create_comparison_plots(comparisons, save_individual=True)
    print(f"✅ Comparison plots saved: {plot_path}")
    
    # Generate comprehensive report
    report_path = report_generator.generate_fitting_report(fitter, comprehensive_result, comparisons)
    json_path = report_generator.save_results_json(comprehensive_result, comparisons)
    print(f"✅ HTML report saved: {report_path}")
    print(f"✅ JSON results saved: {json_path}")
    
    # Final summary
    print(f"\n🎉 IMPROVED FITTING COMPLETED!")
    print("=" * 50)
    print(f"📊 Total fitted parameters: {len(all_fitted_params)}")
    print(f"📊 Characteristics analyzed: {len(comparisons)}")
    
    if comparisons:
        avg_r2 = np.mean([c.r_squared for c in comparisons])
        print(f"📊 Overall fitting quality (avg R²): {avg_r2:.4f}")
        
        print(f"\n📈 Individual Characteristic Quality:")
        for comp in comparisons:
            quality = "🟢 Excellent" if comp.r_squared > 0.99 else ("🟡 Good" if comp.r_squared > 0.95 else ("🟠 Fair" if comp.r_squared > 0.8 else "🔴 Poor"))
            print(f"   {comp.characteristic:<8}: R² = {comp.r_squared:.4f}, RMSE = {comp.rmse:.3e} {quality}")
    
    print(f"\n📁 Output files:")
    print(f"   - Improved model: output/models/improved_fitted_model.lib")
    print(f"   - HTML report: {report_path}")
    print(f"   - JSON results: {json_path}")
    print(f"   - Plots: output/plots/ (with log-scale Y-axis)")
    
    return True


if __name__ == "__main__":
    success = run_improved_fitting()
    sys.exit(0 if success else 1)
