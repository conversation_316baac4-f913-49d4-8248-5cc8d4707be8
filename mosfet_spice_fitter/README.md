# MOSFET SPICE Parameter Fitter

A comprehensive Python project for generating SPICE models for Power MOSFETs by fitting electrical parameters using measurement data and ngspice simulations.

## Overview

This project implements a complete workflow for MOSFET parameter fitting that:

1. **Analyzes existing SPICE models** to understand peripheral circuit structures
2. **Parses electrical measurement data** from various test files
3. **Creates parameterizable SPICE model templates** with fittable parameters
4. **Generates test circuits** for different electrical characteristics
5. **Runs ngspice simulations** to compare with measured data
6. **Optimizes parameters** using scipy.optimize.least_squares
7. **Generates comprehensive reports** with plots and fitted models

## Features

- **Multi-characteristic fitting**: IdVg, IdVd, BVDSS, Coss, Ciss, Crss
- **Robust parameter optimization** using Trust Region Reflective algorithm
- **Comprehensive output generation**: HTML reports, JSON results, comparison plots
- **Modular architecture** with well-separated components
- **Command-line interface** with flexible options
- **Integration testing** to verify all components work together

## Requirements

- Python 3.13+
- ngspice (must be installed and available in PATH)
- uv (for Python package management)

### Python Dependencies

- numpy >= 2.3.1
- scipy >= 1.16.0
- matplotlib >= 3.10.3
- pandas >= 2.3.1

## Installation

1. **Clone or create the project directory**:
   ```bash
   # The project is already set up in the current directory
   cd mosfet_spice_fitter
   ```

2. **Install dependencies using uv**:
   ```bash
   uv sync
   ```

3. **Verify ngspice installation**:
   ```bash
   uv run python main.py --check-only
   ```

## Project Structure

```
mosfet_spice_fitter/
├── src/mosfet_fitter/
│   ├── data_parsers/           # Data parsing modules
│   │   ├── electrical_data_parser.py
│   │   └── spice_lib_parser.py
│   ├── model_templates/        # SPICE model template system
│   │   └── spice_template.py
│   ├── spice_interface/        # ngspice interface
│   │   └── ngspice_interface.py
│   ├── test_circuits/          # Test circuit generation
│   │   └── circuit_generator.py
│   ├── optimization/           # Parameter fitting engine
│   │   └── parameter_fitter.py
│   └── output_generation/      # Report and plot generation
│       └── report_generator.py
├── raw_data/                   # Input measurement data
├── output/                     # Generated outputs
│   ├── models/                 # Fitted SPICE models
│   ├── circuits/               # Generated test circuits
│   ├── plots/                  # Comparison plots
│   └── reports/                # HTML and JSON reports
├── main.py                     # Main workflow script
├── test_integration.py         # Integration test script
└── README.md
```

## Usage

### Quick Start

Run the complete parameter fitting workflow with default settings:

```bash
uv run python main.py
```

### Command Line Options

```bash
# Run with verbose output
uv run python main.py --verbose

# Use custom data directory
uv run python main.py --data-dir custom_data

# Use custom output directory
uv run python main.py --output-dir results

# Fit only specific parameters
uv run python main.py --params Vto Kp U0

# Check prerequisites only
uv run python main.py --check-only
```

### Integration Testing

Before running the full workflow, you can test all components:

```bash
uv run python test_integration.py
```

## Input Data Format

The project expects measurement data in the `raw_data` directory:

- **IdVg.txt**: Drain current vs gate voltage data
- **IdVd.txt**: Drain current vs drain voltage data
- **BVDSS.txt**: Breakdown voltage data
- **Cap_data.txt**: Capacitance data (Cgs, Cds, Cgd vs Vds)
- **SWA04K006_for_spectre.lib**: Reference SPICE library file

## Output Files

After running the workflow, the following files are generated:

### Models
- `output/models/fitted_model.lib`: Complete fitted SPICE model

### Reports
- `output/reports/fitting_report.html`: Comprehensive HTML report
- `output/reports/fitting_results.json`: Machine-readable results

### Plots
- `output/plots/all_characteristics_comparison.png`: Combined comparison plot
- `output/plots/{characteristic}_comparison.png`: Individual characteristic plots

### Test Circuits
- `output/circuits/{test}_test.cir`: Generated test circuits for each characteristic

## Key Components

### 1. Data Parsers
- **ElectricalDataParser**: Parses measurement data from text files
- **SpiceLibParser**: Analyzes existing SPICE library files

### 2. Model Template System
- **SpiceModelTemplate**: Creates parameterizable SPICE models
- **ParameterTemplate**: Defines fittable parameters with bounds

### 3. SPICE Interface
- **NgspiceInterface**: Python interface to ngspice simulator
- Supports DC, AC, and transient analysis

### 4. Test Circuit Generation
- **CircuitGenerator**: Creates test circuits for each characteristic
- Generates appropriate bias conditions and sweep parameters

### 5. Parameter Optimization
- **ParameterFitter**: Implements least-squares parameter fitting
- Uses scipy.optimize.least_squares with Trust Region Reflective algorithm

### 6. Output Generation
- **ReportGenerator**: Creates comprehensive reports and plots
- Generates HTML reports, JSON results, and comparison plots

## Fitting Process

1. **Data Loading**: Parse measurement data and SPICE library
2. **Model Generation**: Create parameterizable SPICE model template
3. **Circuit Creation**: Generate test circuits for each characteristic
4. **Parameter Optimization**:
   - Run ngspice simulations with current parameters
   - Compare with measurement data
   - Update parameters using least-squares optimization
   - Iterate until convergence
5. **Output Generation**: Create fitted model, reports, and plots

## Customization

### Adding New Characteristics

1. Add data parser in `electrical_data_parser.py`
2. Create test circuit in `circuit_generator.py`
3. Update parameter fitter to handle new characteristic
4. Add plotting support in `report_generator.py`

### Modifying Parameters

Edit the parameter templates in `spice_template.py`:
- Add new parameters to relevant dictionaries
- Set appropriate initial values and bounds
- Update model generation logic

### Changing Optimization Settings

Modify settings in `parameter_fitter.py`:
- `max_iterations`: Maximum optimization iterations
- `tolerance`: Convergence tolerance
- Parameter bounds and weights

## Troubleshooting

### Common Issues

1. **ngspice not found**: Ensure ngspice is installed and in PATH
2. **Simulation failures**: Check SPICE model syntax and parameter bounds
3. **Poor fitting quality**: Adjust parameter bounds or add more parameters
4. **Memory issues**: Reduce data size or parameter count

### Debug Mode

Run with verbose logging to see detailed information:

```bash
uv run python main.py --verbose
```

Check the log file at `output/fitting.log` for detailed execution information.

## Performance

- **Typical fitting time**: 1-10 minutes depending on parameter count
- **Memory usage**: ~100-500 MB for typical datasets
- **Scalability**: Handles 10-50 parameters efficiently

## Contributing

The project is modular and extensible. Key areas for contribution:

1. **New measurement types**: Add support for additional electrical characteristics
2. **Optimization algorithms**: Implement alternative fitting methods
3. **Model templates**: Support for different MOSFET model types
4. **Visualization**: Enhanced plotting and reporting features

## License

This project is provided as-is for educational and research purposes.

## Acknowledgments

- Uses ngspice for SPICE simulation
- Built with scipy for numerical optimization
- Matplotlib for plotting and visualization
- Pandas for data manipulation