#!/usr/bin/env python3
"""
Display Fitting Results Summary

This script shows a comprehensive summary of the MOSFET parameter fitting results,
including before/after comparisons and quality metrics.
"""

import json
import sys
from pathlib import Path
import numpy as np

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.mosfet_fitter.data_parsers.electrical_data_parser import ElectricalDataParser


def load_fitting_results():
    """Load the fitting results from JSON file."""
    results_file = Path("output/reports/fitting_results.json")
    if not results_file.exists():
        print("❌ No fitting results found. Please run the fitting first.")
        return None
    
    with open(results_file, 'r') as f:
        return json.load(f)


def show_parameter_comparison(results):
    """Show before/after parameter comparison."""
    print("📊 PARAMETER FITTING RESULTS")
    print("=" * 80)
    
    fitted_params = results['fitted_parameters']
    initial_params = results['initial_parameters']
    
    print(f"{'Parameter':<20} {'Initial Value':<15} {'Fitted Value':<15} {'Change (%)':<12} {'Category'}")
    print("-" * 80)
    
    # Group parameters by category
    categories = {
        'Capacitance': [p for p in fitted_params.keys() if p.startswith('param_C')],
        'MOSFET': [p for p in fitted_params.keys() if p in ['Vto', 'Kp', 'U0', 'Gamma', 'Phi']],
        'Diode': [p for p in fitted_params.keys() if p.startswith('DBD_')],
        'Parasitic': [p for p in fitted_params.keys() if p in ['LD', 'RD', 'LG', 'RG', 'LS', 'RS']]
    }
    
    for category, params in categories.items():
        if not params:
            continue
            
        print(f"\n{category} Parameters:")
        for param in params:
            if param in fitted_params and param in initial_params:
                initial = initial_params[param]
                fitted = fitted_params[param]
                
                if initial != 0:
                    change = ((fitted - initial) / initial) * 100
                else:
                    change = float('inf') if fitted != 0 else 0
                
                change_str = f"{change:+.1f}%" if abs(change) < 1000 else "Large"
                
                print(f"  {param:<18} {initial:<15.3e} {fitted:<15.3e} {change_str:<12} {category}")


def show_fitting_quality(results):
    """Show fitting quality metrics."""
    print("\n\n🎯 FITTING QUALITY METRICS")
    print("=" * 50)
    
    summary = results['fitting_summary']
    print(f"✅ Fitting Status: {'SUCCESS' if summary['success'] else 'FAILED'}")
    print(f"🔄 Total Iterations: {summary['iterations']}")
    print(f"⏱️  Total Time: {summary['fitting_time']:.2f} seconds")
    print(f"📉 Final Cost: {summary['final_cost']:.6e}")
    
    if 'characteristic_metrics' in results:
        print(f"\n📊 Individual Characteristic Quality:")
        metrics = results['characteristic_metrics']
        
        for char_name, char_metrics in metrics.items():
            r2 = char_metrics['r_squared']
            rmse = char_metrics['rmse']
            points = char_metrics['data_points']
            
            # Quality assessment
            if r2 > 0.99:
                quality = "🟢 Excellent"
            elif r2 > 0.95:
                quality = "🟡 Good"
            elif r2 > 0.8:
                quality = "🟠 Fair"
            else:
                quality = "🔴 Poor"
            
            print(f"  {char_name:<8}: R² = {r2:.4f}, RMSE = {rmse:.3e}, Points = {points:3d} {quality}")


def show_measurement_data_summary():
    """Show summary of measurement data."""
    print("\n\n📈 MEASUREMENT DATA SUMMARY")
    print("=" * 50)
    
    try:
        parser = ElectricalDataParser("raw_data")
        all_data = parser.get_all_electrical_data()
        
        for data_type, data in all_data.items():
            if data:
                print(f"\n{data_type}:")
                if isinstance(data, dict):
                    for key, value in data.items():
                        if isinstance(value, np.ndarray):
                            min_val = np.min(value)
                            max_val = np.max(value)
                            print(f"  {key}: {len(value)} points, range: {min_val:.3e} to {max_val:.3e}")
                        elif isinstance(value, dict):
                            print(f"  {key}: {len(value)} sub-datasets")
                        else:
                            print(f"  {key}: {value}")
    except Exception as e:
        print(f"❌ Error loading measurement data: {e}")


def show_recommendations(results):
    """Show recommendations for further improvement."""
    print("\n\n💡 RECOMMENDATIONS FOR IMPROVEMENT")
    print("=" * 50)
    
    if 'characteristic_metrics' in results:
        metrics = results['characteristic_metrics']
        
        poor_fits = [name for name, data in metrics.items() if data['r_squared'] < 0.9]
        good_fits = [name for name, data in metrics.items() if data['r_squared'] >= 0.99]
        
        if poor_fits:
            print(f"🔧 Characteristics needing improvement: {', '.join(poor_fits)}")
            print("   - Consider adjusting parameter bounds")
            print("   - Check measurement data quality")
            print("   - Add more parameters to the fitting")
        
        if good_fits:
            print(f"✅ Well-fitted characteristics: {', '.join(good_fits)}")
        
        print(f"\n🎯 Next steps:")
        print("   1. Validate fitted model with independent test data")
        print("   2. Check model behavior at extreme operating conditions")
        print("   3. Consider temperature dependence if needed")
        print("   4. Verify model convergence in circuit simulations")
    
    # Check parameter changes
    fitted_params = results['fitted_parameters']
    initial_params = results['initial_parameters']
    
    large_changes = []
    for param, fitted_val in fitted_params.items():
        if param in initial_params:
            initial_val = initial_params[param]
            if initial_val != 0:
                change = abs((fitted_val - initial_val) / initial_val)
                if change > 2.0:  # More than 200% change
                    large_changes.append(param)
    
    if large_changes:
        print(f"\n⚠️  Parameters with large changes: {', '.join(large_changes)}")
        print("   - Review initial parameter estimates")
        print("   - Check parameter bounds")
        print("   - Validate physical reasonableness")


def main():
    """Main function to display fitting results."""
    print("🔍 MOSFET PARAMETER FITTING RESULTS SUMMARY")
    print("=" * 80)
    
    # Load results
    results = load_fitting_results()
    if not results:
        return False
    
    # Show different aspects of the results
    show_parameter_comparison(results)
    show_fitting_quality(results)
    show_measurement_data_summary()
    show_recommendations(results)
    
    # Final summary
    print("\n\n🎉 SUMMARY")
    print("=" * 30)
    
    if results['fitting_summary']['success']:
        if 'characteristic_metrics' in results:
            avg_r2 = np.mean([data['r_squared'] for data in results['characteristic_metrics'].values()])
            print(f"📊 Overall fitting quality: R² = {avg_r2:.4f}")
            
            if avg_r2 > 0.99:
                print("🏆 Excellent fitting achieved!")
            elif avg_r2 > 0.95:
                print("✅ Good fitting achieved!")
            elif avg_r2 > 0.8:
                print("⚠️  Acceptable fitting, room for improvement")
            else:
                print("❌ Poor fitting, significant improvement needed")
        
        print(f"📁 Check output files in: output/")
        print(f"   - HTML report: output/reports/fitting_report.html")
        print(f"   - Fitted model: output/models/comprehensive_fitted_model.lib")
        print(f"   - Plots: output/plots/")
    else:
        print("❌ Fitting was not successful")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
