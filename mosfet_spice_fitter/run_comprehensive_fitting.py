#!/usr/bin/env python3
"""
Comprehensive MOSFET Parameter Fitting Script

This script runs a more comprehensive parameter fitting that includes
both electrical characteristics and capacitance parameters with better
optimization strategies.
"""

import sys
from pathlib import Path
import numpy as np

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.mosfet_fitter.optimization.parameter_fitter import ParameterFitter
from src.mosfet_fitter.output_generation.report_generator import ReportGenerator


def run_comprehensive_fitting():
    """Run comprehensive parameter fitting with multiple stages."""
    
    print("🚀 Starting Comprehensive MOSFET Parameter Fitting")
    print("=" * 60)
    
    # Initialize components
    fitter = ParameterFitter("raw_data", "output/models/comprehensive_fitted_model.lib")
    report_generator = ReportGenerator("output")
    
    print(f"📊 Loaded measurement data: {list(fitter.measurement_data.keys())}")
    
    # Stage 1: Fit capacitance parameters first (they're most problematic)
    print("\n🔧 Stage 1: Fitting Capacitance Parameters")
    print("-" * 40)
    
    capacitance_params = [
        'param_Cgs_0', 'param_Cgs_1', 'param_Cgs_2',
        'param_Cds_0', 'param_Cds_1', 'param_Cds_2',
        'param_Cgd_0', 'param_Cgd_1', 'param_Cgd_2',
    ]
    
    cap_result = fitter.fit_parameters(capacitance_params)
    
    if cap_result.success:
        print(f"✅ Capacitance fitting successful!")
        print(f"   Final cost: {cap_result.final_cost:.6e}")
        print(f"   Iterations: {cap_result.iterations}")
        print(f"   Time: {cap_result.fitting_time:.2f} seconds")
        
        # Show parameter changes
        print("\n📈 Capacitance Parameter Changes:")
        for name, value in cap_result.fitted_parameters.items():
            initial = cap_result.initial_parameters[name]
            change = ((value - initial) / initial) * 100 if initial != 0 else float('inf')
            print(f"   {name}: {initial:.3e} → {value:.3e} ({change:+.1f}%)")
    else:
        print(f"❌ Capacitance fitting failed: {cap_result.convergence_info}")
        return False
    
    # Stage 2: Fit key MOSFET parameters while keeping capacitance fixed
    print("\n🔧 Stage 2: Fitting Key MOSFET Parameters")
    print("-" * 40)
    
    # Update fitter with fitted capacitance parameters
    all_fittable = fitter.model_template.get_all_fittable_parameters()
    for name, value in cap_result.fitted_parameters.items():
        if name in all_fittable:
            all_fittable[name].initial_value = value
            all_fittable[name].is_fittable = False  # Fix capacitance parameters
    
    # Now fit key MOSFET parameters
    mosfet_params = [
        'Vto',      # Threshold voltage - critical for IdVg
        'Kp',       # Transconductance parameter - critical for current levels
        'U0',       # Surface mobility - affects current characteristics
        'Gamma',    # Body effect parameter
        'Phi',      # Surface potential
    ]
    
    mosfet_result = fitter.fit_parameters(mosfet_params)
    
    if mosfet_result.success:
        print(f"✅ MOSFET parameter fitting successful!")
        print(f"   Final cost: {mosfet_result.final_cost:.6e}")
        print(f"   Iterations: {mosfet_result.iterations}")
        print(f"   Time: {mosfet_result.fitting_time:.2f} seconds")
        
        # Show parameter changes
        print("\n📈 MOSFET Parameter Changes:")
        for name, value in mosfet_result.fitted_parameters.items():
            initial = mosfet_result.initial_parameters[name]
            change = ((value - initial) / initial) * 100 if initial != 0 else float('inf')
            print(f"   {name}: {initial:.3e} → {value:.3e} ({change:+.1f}%)")
    else:
        print(f"❌ MOSFET parameter fitting failed: {mosfet_result.convergence_info}")
        # Continue with capacitance-only results
        mosfet_result = cap_result
    
    # Stage 3: Fine-tune with all parameters together (optional)
    print("\n🔧 Stage 3: Fine-tuning All Parameters")
    print("-" * 40)
    
    # Re-enable all parameters for fine-tuning
    for name in all_fittable:
        all_fittable[name].is_fittable = True
    
    # Combine all fitted parameters as starting point
    combined_params = {}
    combined_params.update(cap_result.fitted_parameters)
    if mosfet_result != cap_result:
        combined_params.update(mosfet_result.fitted_parameters)
    
    # Update initial values with fitted results
    for name, value in combined_params.items():
        if name in all_fittable:
            all_fittable[name].initial_value = value
    
    # Fine-tune with a smaller subset to avoid over-fitting
    fine_tune_params = [
        'param_Cgs_0', 'param_Cds_0', 'param_Cgd_0',  # Main capacitance values
        'Vto', 'Kp',  # Key MOSFET parameters
    ]
    
    final_result = fitter.fit_parameters(fine_tune_params)
    
    if final_result.success:
        print(f"✅ Fine-tuning successful!")
        print(f"   Final cost: {final_result.final_cost:.6e}")
        print(f"   Iterations: {final_result.iterations}")
        print(f"   Time: {final_result.fitting_time:.2f} seconds")
    else:
        print(f"⚠️  Fine-tuning failed, using previous results")
        final_result = mosfet_result if mosfet_result != cap_result else cap_result
    
    # Combine all fitted parameters for final model
    all_fitted_params = {}
    all_fitted_params.update(cap_result.fitted_parameters)
    if mosfet_result != cap_result:
        all_fitted_params.update(mosfet_result.fitted_parameters)
    all_fitted_params.update(final_result.fitted_parameters)
    
    # Create final result object
    from src.mosfet_fitter.optimization.parameter_fitter import FittingResult
    comprehensive_result = FittingResult(
        success=True,
        fitted_parameters=all_fitted_params,
        initial_parameters={**cap_result.initial_parameters, 
                          **(mosfet_result.initial_parameters if mosfet_result != cap_result else {}),
                          **final_result.initial_parameters},
        final_cost=final_result.final_cost,
        iterations=cap_result.iterations + (mosfet_result.iterations if mosfet_result != cap_result else 0) + final_result.iterations,
        convergence_info=final_result.convergence_info,
        fitting_time=cap_result.fitting_time + (mosfet_result.fitting_time if mosfet_result != cap_result else 0) + final_result.fitting_time
    )
    
    # Generate comprehensive model and reports
    print("\n📊 Generating Comprehensive Results")
    print("-" * 40)
    
    # Save fitted model
    fitter.save_fitted_model(comprehensive_result, "output/models/comprehensive_fitted_model.lib")
    print("✅ Comprehensive fitted model saved")
    
    # Generate comparison data and plots
    comparisons = report_generator.generate_comparison_data(fitter, comprehensive_result)
    plot_path = report_generator.create_comparison_plots(comparisons, save_individual=True)
    print(f"✅ Comparison plots saved: {plot_path}")
    
    # Generate comprehensive report
    report_path = report_generator.generate_fitting_report(fitter, comprehensive_result, comparisons)
    json_path = report_generator.save_results_json(comprehensive_result, comparisons)
    print(f"✅ HTML report saved: {report_path}")
    print(f"✅ JSON results saved: {json_path}")
    
    # Final summary
    print("\n🎉 COMPREHENSIVE FITTING COMPLETED!")
    print("=" * 60)
    print(f"📊 Total fitted parameters: {len(all_fitted_params)}")
    print(f"📊 Characteristics analyzed: {len(comparisons)}")
    
    if comparisons:
        avg_r2 = np.mean([c.r_squared for c in comparisons])
        print(f"📊 Overall fitting quality (avg R²): {avg_r2:.4f}")
        
        print("\n📈 Individual Characteristic Quality:")
        for comp in comparisons:
            print(f"   {comp.characteristic}: R² = {comp.r_squared:.4f}, RMSE = {comp.rmse:.3e}")
    
    print(f"\n📁 Output files:")
    print(f"   - Comprehensive model: output/models/comprehensive_fitted_model.lib")
    print(f"   - HTML report: {report_path}")
    print(f"   - JSON results: {json_path}")
    print(f"   - Plots: output/plots/")
    
    return True


if __name__ == "__main__":
    success = run_comprehensive_fitting()
    sys.exit(0 if success else 1)
