#!/usr/bin/env python3
"""
Debug script to check which characteristics can be simulated
"""

import sys
from pathlib import Path
import numpy as np

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.mosfet_fitter.optimization.parameter_fitter import ParameterFitter


def debug_characteristics():
    """Debug each characteristic simulation."""
    
    print("🔍 Debugging Characteristic Simulations")
    print("=" * 50)
    
    # Initialize fitter
    fitter = ParameterFitter("raw_data", "output/models/debug_model.lib")
    
    print(f"📊 Available measurement data: {list(fitter.measurement_data.keys())}")
    
    # Get default parameters
    all_fittable = fitter.model_template.get_all_fittable_parameters()
    default_parameters = {}
    for name, param in all_fittable.items():
        default_parameters[name] = param.initial_value
    
    print(f"\n🔧 Testing simulation for each characteristic:")
    print("-" * 50)
    
    # Test each characteristic
    for char_name, measurement in fitter.measurement_data.items():
        if measurement is None or len(measurement.x_data) == 0:
            print(f"❌ {char_name}: No measurement data")
            continue
            
        print(f"\n🧪 Testing {char_name}:")
        print(f"   Measurement points: {len(measurement.x_data)}")
        print(f"   X range: {np.min(measurement.x_data):.3e} to {np.max(measurement.x_data):.3e}")
        print(f"   Y range: {np.min(measurement.y_data):.3e} to {np.max(measurement.y_data):.3e}")
        
        try:
            # Try to simulate this characteristic
            sim_result = fitter.simulate_characteristic(default_parameters, char_name)
            
            if sim_result is not None:
                sim_x, sim_y = sim_result
                print(f"   ✅ Simulation successful!")
                print(f"   Simulation points: {len(sim_x)}")
                print(f"   Sim X range: {np.min(sim_x):.3e} to {np.max(sim_x):.3e}")
                print(f"   Sim Y range: {np.min(sim_y):.3e} to {np.max(sim_y):.3e}")
                
                # Check for reasonable values
                if np.any(np.isnan(sim_y)) or np.any(np.isinf(sim_y)):
                    print(f"   ⚠️  Warning: NaN or Inf values in simulation")
                
                if np.any(sim_y < 0) and char_name in ['Cgs', 'Cds', 'Cgd']:
                    print(f"   ⚠️  Warning: Negative capacitance values")
                
                # Calculate basic fit quality
                if len(sim_x) > 1 and len(measurement.x_data) > 1:
                    try:
                        sim_y_interp = np.interp(measurement.x_data, sim_x, sim_y)
                        
                        # Calculate R²
                        ss_res = np.sum((measurement.y_data - sim_y_interp) ** 2)
                        ss_tot = np.sum((measurement.y_data - np.mean(measurement.y_data)) ** 2)
                        r_squared = 1 - (ss_res / (ss_tot + 1e-12))
                        
                        print(f"   📊 Initial R²: {r_squared:.4f}")
                        
                        if r_squared < 0:
                            print(f"   ⚠️  Poor initial fit - simulation worse than mean")
                        
                    except Exception as e:
                        print(f"   ❌ Error calculating fit quality: {e}")
                
            else:
                print(f"   ❌ Simulation failed - returned None")
                
        except Exception as e:
            print(f"   ❌ Simulation error: {e}")
    
    print(f"\n📋 Summary:")
    print("-" * 30)
    
    # Test comparison data generation
    try:
        from src.mosfet_fitter.output_generation.report_generator import ReportGenerator
        from src.mosfet_fitter.optimization.parameter_fitter import FittingResult
        
        # Create a dummy fitting result
        dummy_result = FittingResult(
            success=True,
            fitted_parameters=default_parameters,
            initial_parameters=default_parameters,
            final_cost=1.0,
            iterations=1,
            convergence_info={"message": "debug"},
            fitting_time=1.0
        )
        
        report_generator = ReportGenerator("output")
        comparisons = report_generator.generate_comparison_data(fitter, dummy_result)
        
        print(f"✅ Generated {len(comparisons)} comparisons:")
        for comp in comparisons:
            print(f"   - {comp.characteristic}: R² = {comp.r_squared:.4f}")
            
    except Exception as e:
        print(f"❌ Error generating comparisons: {e}")


if __name__ == "__main__":
    debug_characteristics()
