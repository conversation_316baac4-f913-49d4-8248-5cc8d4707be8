#!/usr/bin/env python3
"""
Complete MOSFET Parameter Fitting Script

This script performs comprehensive parameter fitting including:
1. Capacitance parameters (Cgs, Cds, Cgd)
2. Static MOSFET parameters (Vto, Kp, U0, etc.) for IdVg and IdVd
3. Diode parameters (DBD_Bv, etc.) for BVDSS
"""

import sys
from pathlib import Path
import numpy as np

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.mosfet_fitter.optimization.parameter_fitter import ParameterFitter
from src.mosfet_fitter.output_generation.report_generator import ReportGenerator


def run_complete_fitting():
    """Run complete parameter fitting with all characteristics."""
    
    print("🚀 COMPLETE MOSFET PARAMETER FITTING")
    print("   - Capacitance parameters: Cgs, Cds, Cgd")
    print("   - Static MOSFET parameters: Vto, Kp, U0, Gamma, Phi")
    print("   - Diode parameters: DBD_Bv, DBD_Is, DBD_N")
    print("   - All characteristics: IdVg, IdVd, BVDSS, Capacitances")
    print("=" * 70)
    
    # Initialize components
    fitter = ParameterFitter("raw_data", "output/models/complete_fitted_model.lib")
    report_generator = ReportGenerator("output")
    
    print(f"📊 Loaded measurement data: {list(fitter.measurement_data.keys())}")
    
    # Check which characteristics have data and can be simulated
    available_chars = []
    working_chars = []
    
    # Get default parameters for testing
    all_fittable = fitter.model_template.get_all_fittable_parameters()
    default_parameters = {}
    for name, param in all_fittable.items():
        default_parameters[name] = param.initial_value
    
    for char_name, measurement in fitter.measurement_data.items():
        if measurement is not None and len(measurement.x_data) > 0:
            available_chars.append(char_name)
            
            # Test if simulation works
            try:
                sim_result = fitter.simulate_characteristic(default_parameters, char_name)
                if sim_result is not None:
                    working_chars.append(char_name)
                    print(f"   ✅ {char_name}: {len(measurement.x_data)} points - Simulation OK")
                else:
                    print(f"   ⚠️  {char_name}: {len(measurement.x_data)} points - Simulation Failed")
            except Exception as e:
                print(f"   ❌ {char_name}: {len(measurement.x_data)} points - Error: {e}")
        else:
            print(f"   ❌ {char_name}: No data")
    
    if not working_chars:
        print("❌ No working characteristic simulations!")
        return False
    
    print(f"\n📊 Working characteristics: {working_chars}")
    
    # Stage 1: Fit capacitance parameters
    print(f"\n🔧 Stage 1: Capacitance Parameter Fitting")
    print("-" * 50)
    
    capacitance_params = []
    for cap_type in ['Cgs', 'Cds', 'Cgd']:
        if cap_type in working_chars:
            # Use first 3 orders (0, 1, 2) for stability
            for order in [0, 1]:  # Start with just constant and linear
                param_name = f'param_{cap_type}_{order}'
                if param_name in all_fittable:
                    capacitance_params.append(param_name)
    
    print(f"   Fitting {len(capacitance_params)} capacitance parameters")
    
    cap_result = None
    if capacitance_params:
        try:
            cap_result = fitter.fit_parameters(capacitance_params)
            
            if cap_result.success:
                print(f"✅ Capacitance fitting successful!")
                print(f"   Final cost: {cap_result.final_cost:.6e}")
                print(f"   Iterations: {cap_result.iterations}")
                print(f"   Time: {cap_result.fitting_time:.2f} seconds")
                
                # Update parameters
                for name, value in cap_result.fitted_parameters.items():
                    if name in all_fittable:
                        all_fittable[name].initial_value = value
            else:
                print(f"❌ Capacitance fitting failed: {cap_result.convergence_info}")
                cap_result = None
        except Exception as e:
            print(f"❌ Capacitance fitting error: {e}")
            cap_result = None
    
    # Stage 2: Fit static MOSFET parameters
    print(f"\n🔧 Stage 2: Static MOSFET Parameter Fitting")
    print("-" * 50)
    
    mosfet_params = []
    if any(char in working_chars for char in ['IdVg', 'IdVd']):
        # Key static parameters that affect current characteristics
        candidate_params = ['Vto', 'Kp', 'U0']  # Start with most important ones
        mosfet_params = [p for p in candidate_params if p in all_fittable]
    
    print(f"   Fitting {len(mosfet_params)} MOSFET parameters")
    
    mosfet_result = None
    if mosfet_params:
        try:
            mosfet_result = fitter.fit_parameters(mosfet_params)
            
            if mosfet_result.success:
                print(f"✅ MOSFET parameter fitting successful!")
                print(f"   Final cost: {mosfet_result.final_cost:.6e}")
                print(f"   Iterations: {mosfet_result.iterations}")
                print(f"   Time: {mosfet_result.fitting_time:.2f} seconds")
                
                # Update parameters
                for name, value in mosfet_result.fitted_parameters.items():
                    if name in all_fittable:
                        all_fittable[name].initial_value = value
            else:
                print(f"❌ MOSFET parameter fitting failed: {mosfet_result.convergence_info}")
                mosfet_result = None
        except Exception as e:
            print(f"❌ MOSFET parameter fitting error: {e}")
            mosfet_result = None
    else:
        print("   ⚠️  No MOSFET parameters to fit (IdVg/IdVd not working)")
    
    # Stage 3: Fit diode parameters
    print(f"\n🔧 Stage 3: Diode Parameter Fitting")
    print("-" * 50)
    
    diode_params = []
    if 'BVDSS' in working_chars:
        # Key diode parameters for breakdown
        candidate_params = ['DBD_Bv']  # Start with breakdown voltage only
        diode_params = [p for p in candidate_params if p in all_fittable]
    
    print(f"   Fitting {len(diode_params)} diode parameters")
    
    diode_result = None
    if diode_params:
        try:
            diode_result = fitter.fit_parameters(diode_params)
            
            if diode_result.success:
                print(f"✅ Diode parameter fitting successful!")
                print(f"   Final cost: {diode_result.final_cost:.6e}")
                print(f"   Iterations: {diode_result.iterations}")
                print(f"   Time: {diode_result.fitting_time:.2f} seconds")
                
                # Update parameters
                for name, value in diode_result.fitted_parameters.items():
                    if name in all_fittable:
                        all_fittable[name].initial_value = value
            else:
                print(f"❌ Diode parameter fitting failed: {diode_result.convergence_info}")
                diode_result = None
        except Exception as e:
            print(f"❌ Diode parameter fitting error: {e}")
            diode_result = None
    else:
        print("   ⚠️  No diode parameters to fit (BVDSS not working)")
    
    # Combine all results
    all_fitted_params = {}
    total_iterations = 0
    total_time = 0.0
    successful_results = []
    
    for result in [cap_result, mosfet_result, diode_result]:
        if result and result.success:
            all_fitted_params.update(result.fitted_parameters)
            total_iterations += result.iterations
            total_time += result.fitting_time
            successful_results.append(result)
    
    if not all_fitted_params:
        print("❌ No successful fitting results!")
        return False
    
    # Create comprehensive result
    from src.mosfet_fitter.optimization.parameter_fitter import FittingResult
    
    last_result = successful_results[-1]
    
    # Get initial parameters
    initial_params = {}
    for name in all_fitted_params.keys():
        if name in all_fittable:
            initial_params[name] = all_fittable[name].initial_value
    
    comprehensive_result = FittingResult(
        success=True,
        fitted_parameters=all_fitted_params,
        initial_parameters=initial_params,
        final_cost=last_result.final_cost,
        iterations=total_iterations,
        convergence_info=last_result.convergence_info,
        fitting_time=total_time
    )
    
    # Generate results
    print(f"\n📊 Generating Complete Results")
    print("-" * 40)
    
    try:
        # Save fitted model
        fitter.save_fitted_model(comprehensive_result, "output/models/complete_fitted_model.lib")
        print("✅ Complete fitted model saved")
        
        # Generate comparison data and plots
        comparisons = report_generator.generate_comparison_data(fitter, comprehensive_result)
        print(f"✅ Generated {len(comparisons)} characteristic comparisons")
        
        plot_path = report_generator.create_comparison_plots(comparisons, save_individual=True)
        print(f"✅ Comparison plots saved: {plot_path}")
        
        # Generate comprehensive report
        report_path = report_generator.generate_fitting_report(fitter, comprehensive_result, comparisons)
        json_path = report_generator.save_results_json(comprehensive_result, comparisons)
        print(f"✅ HTML report saved: {report_path}")
        print(f"✅ JSON results saved: {json_path}")
        
    except Exception as e:
        print(f"❌ Error generating results: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Final summary
    print(f"\n🎉 COMPLETE FITTING FINISHED!")
    print("=" * 50)
    print(f"📊 Total fitted parameters: {len(all_fitted_params)}")
    print(f"📊 Characteristics analyzed: {len(comparisons)}")
    print(f"📊 Working characteristics: {working_chars}")
    
    if comparisons:
        r_squared_values = [c.r_squared for c in comparisons if not np.isnan(c.r_squared) and not np.isinf(c.r_squared)]
        
        if r_squared_values:
            avg_r2 = np.mean(r_squared_values)
            print(f"📊 Overall fitting quality (avg R²): {avg_r2:.4f}")
            
            print(f"\n📈 Individual Characteristic Quality:")
            for comp in comparisons:
                if np.isnan(comp.r_squared) or np.isinf(comp.r_squared):
                    quality = "🔴 Invalid"
                    r2_str = "Invalid"
                else:
                    quality = "🟢 Excellent" if comp.r_squared > 0.99 else ("🟡 Good" if comp.r_squared > 0.95 else ("🟠 Fair" if comp.r_squared > 0.8 else "🔴 Poor"))
                    r2_str = f"{comp.r_squared:.4f}"
                
                print(f"   {comp.characteristic:<8}: R² = {r2_str:<8}, RMSE = {comp.rmse:.3e} {quality}")
    
    # Show fitted parameters by category
    print(f"\n📋 FITTED PARAMETERS BY CATEGORY:")
    print("-" * 50)
    
    cap_params = {k: v for k, v in all_fitted_params.items() if k.startswith('param_C')}
    mosfet_params_fitted = {k: v for k, v in all_fitted_params.items() if k in ['Vto', 'Kp', 'U0', 'Gamma', 'Phi']}
    diode_params_fitted = {k: v for k, v in all_fitted_params.items() if k.startswith('DBD_')}
    
    if cap_params:
        print(f"🔹 Capacitance Parameters ({len(cap_params)}):")
        for name, value in cap_params.items():
            print(f"   {name}: {value:.6e}")
    
    if mosfet_params_fitted:
        print(f"🔹 Static MOSFET Parameters ({len(mosfet_params_fitted)}):")
        for name, value in mosfet_params_fitted.items():
            print(f"   {name}: {value:.6e}")
    
    if diode_params_fitted:
        print(f"🔹 Diode Parameters ({len(diode_params_fitted)}):")
        for name, value in diode_params_fitted.items():
            print(f"   {name}: {value:.6e}")
    
    print(f"\n📁 Output files:")
    print(f"   - Complete model: output/models/complete_fitted_model.lib")
    print(f"   - HTML report: {report_path}")
    print(f"   - JSON results: {json_path}")
    print(f"   - Plots: output/plots/")
    
    return True


if __name__ == "__main__":
    success = run_complete_fitting()
    sys.exit(0 if success else 1)
