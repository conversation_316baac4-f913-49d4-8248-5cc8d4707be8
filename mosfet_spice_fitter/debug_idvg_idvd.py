#!/usr/bin/env python3
"""
Debug IdVg and IdVd Simulation Issues

This script specifically debugs why IdVg and IdVd simulations are failing.
"""

import sys
from pathlib import Path
import numpy as np
import tempfile

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.mosfet_fitter.optimization.parameter_fitter import ParameterFitter
from src.mosfet_fitter.test_circuits.circuit_generator import CircuitGenerator
from src.mosfet_fitter.spice_interface.ngspice_interface import NgspiceInterface


def debug_idvg_idvd():
    """Debug IdVg and IdVd simulation issues step by step."""
    
    print("🔍 DEBUGGING IdVg AND IdVd SIMULATION ISSUES")
    print("=" * 60)
    
    # Initialize components
    fitter = ParameterFitter("raw_data", "output/models/debug_model.lib")
    
    # Get default parameters
    all_params = fitter.model_template.get_all_fittable_parameters()
    default_parameters = {}
    for name, param in all_params.items():
        default_parameters[name] = param.initial_value
    
    print(f"📊 Total parameters: {len(default_parameters)}")
    
    # Check measurement data
    print(f"\n📈 Measurement Data Status:")
    print("-" * 40)
    for char_name, measurement in fitter.measurement_data.items():
        if measurement is not None and len(measurement.x_data) > 0:
            print(f"✅ {char_name}: {len(measurement.x_data)} points")
            print(f"   X range: {np.min(measurement.x_data):.3f} to {np.max(measurement.x_data):.3f}")
            print(f"   Y range: {np.min(measurement.y_data):.3e} to {np.max(measurement.y_data):.3e}")
        else:
            print(f"❌ {char_name}: No data")
    
    # Test model file generation
    print(f"\n🔧 Testing Model File Generation:")
    print("-" * 40)
    
    try:
        # Create temporary model file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.lib', delete=False) as temp_file:
            temp_model_file = Path(temp_file.name)
            
            # Generate model content
            model_content = fitter.model_template.generate_spice_model(default_parameters)
            temp_file.write(model_content)
            temp_file.flush()
            
        print(f"✅ Model file created: {temp_model_file}")
        print(f"   Size: {temp_model_file.stat().st_size} bytes")
        
        # Show first few lines of model
        with open(temp_model_file, 'r') as f:
            lines = f.readlines()[:10]
            print(f"   First 10 lines:")
            for i, line in enumerate(lines, 1):
                print(f"     {i:2d}: {line.rstrip()}")
        
    except Exception as e:
        print(f"❌ Model file generation failed: {e}")
        return False
    
    # Test circuit generation
    print(f"\n🔌 Testing Circuit Generation:")
    print("-" * 40)
    
    try:
        circuit_gen = CircuitGenerator(str(temp_model_file))
        
        # Test IdVg circuit
        print(f"🧪 Testing IdVg circuit generation:")
        idvg_circuit = circuit_gen.generate_idvg_circuit()
        print(f"   ✅ IdVg circuit generated ({len(idvg_circuit)} chars)")
        print(f"   First few lines:")
        for i, line in enumerate(idvg_circuit.split('\n')[:5], 1):
            print(f"     {i}: {line}")
        
        # Test IdVd circuit
        print(f"\n🧪 Testing IdVd circuit generation:")
        idvd_circuit = circuit_gen.generate_idvd_circuit()
        print(f"   ✅ IdVd circuit generated ({len(idvd_circuit)} chars)")
        print(f"   First few lines:")
        for i, line in enumerate(idvd_circuit.split('\n')[:5], 1):
            print(f"     {i}: {line}")
        
    except Exception as e:
        print(f"❌ Circuit generation failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Test SPICE simulation
    print(f"\n⚡ Testing SPICE Simulation:")
    print("-" * 40)
    
    try:
        ngspice = NgspiceInterface()
        
        # Test IdVg simulation
        print(f"🧪 Testing IdVg simulation:")
        idvg_result = ngspice.run_dc_sweep(
            idvg_circuit, 'VGS', 0.0, 10.0, 0.1, ['v(gate)', 'I(VDS)']
        )
        
        if idvg_result.success:
            print(f"   ✅ IdVg simulation successful!")
            print(f"   Data keys: {list(idvg_result.data.keys())}")
            if 'v(gate)' in idvg_result.data:
                vg_data = idvg_result.data['v(gate)']
                print(f"   Gate voltage: {len(vg_data)} points, range {np.min(vg_data):.3f} to {np.max(vg_data):.3f}")
            if 'I(VDS)' in idvg_result.data:
                id_data = idvg_result.data['I(VDS)']
                print(f"   Drain current: {len(id_data)} points, range {np.min(id_data):.3e} to {np.max(id_data):.3e}")
        else:
            print(f"   ❌ IdVg simulation failed!")
            print(f"   Error: {idvg_result.error_message}")
            print(f"   Output: {idvg_result.raw_output}")
        
        # Test IdVd simulation
        print(f"\n🧪 Testing IdVd simulation:")
        idvd_result = ngspice.run_dc_sweep(
            idvd_circuit, 'VDS', 0.0, 10.0, 0.1, ['v(drain)', 'I(VDS)']
        )
        
        if idvd_result.success:
            print(f"   ✅ IdVd simulation successful!")
            print(f"   Data keys: {list(idvd_result.data.keys())}")
            if 'v(drain)' in idvd_result.data:
                vd_data = idvd_result.data['v(drain)']
                print(f"   Drain voltage: {len(vd_data)} points, range {np.min(vd_data):.3f} to {np.max(vd_data):.3f}")
            if 'I(VDS)' in idvd_result.data:
                id_data = idvd_result.data['I(VDS)']
                print(f"   Drain current: {len(id_data)} points, range {np.min(id_data):.3e} to {np.max(id_data):.3e}")
        else:
            print(f"   ❌ IdVd simulation failed!")
            print(f"   Error: {idvd_result.error_message}")
            print(f"   Output: {idvd_result.raw_output}")
        
    except Exception as e:
        print(f"❌ SPICE simulation error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Test parameter fitter simulation method
    print(f"\n🎯 Testing Parameter Fitter Simulation:")
    print("-" * 40)
    
    for char_name in ['IdVg', 'IdVd']:
        if char_name in fitter.measurement_data and fitter.measurement_data[char_name] is not None:
            print(f"🧪 Testing {char_name} via parameter fitter:")
            try:
                sim_result = fitter.simulate_characteristic(default_parameters, char_name)
                
                if sim_result is not None:
                    sim_x, sim_y = sim_result
                    print(f"   ✅ {char_name} simulation successful!")
                    print(f"   Sim points: {len(sim_x)}")
                    print(f"   X range: {np.min(sim_x):.3f} to {np.max(sim_x):.3f}")
                    print(f"   Y range: {np.min(sim_y):.3e} to {np.max(sim_y):.3e}")
                    
                    # Check for issues
                    if np.any(np.isnan(sim_y)) or np.any(np.isinf(sim_y)):
                        print(f"   ⚠️  Warning: NaN or Inf values in simulation")
                    if np.any(sim_y <= 0):
                        print(f"   ⚠️  Warning: Non-positive current values")
                    
                else:
                    print(f"   ❌ {char_name} simulation returned None")
                    
            except Exception as e:
                print(f"   ❌ {char_name} simulation error: {e}")
                import traceback
                traceback.print_exc()
    
    # Clean up
    try:
        temp_model_file.unlink()
        print(f"\n🧹 Cleaned up temporary file: {temp_model_file}")
    except:
        pass
    
    # Summary and recommendations
    print(f"\n💡 DIAGNOSIS SUMMARY:")
    print("-" * 30)
    
    if idvg_result.success and idvd_result.success:
        print(f"✅ Both IdVg and IdVd simulations work at SPICE level")
        print(f"🔧 Issue likely in parameter fitter integration")
        print(f"💡 Recommendations:")
        print(f"   1. Check parameter fitter simulate_characteristic method")
        print(f"   2. Verify data interpolation and processing")
        print(f"   3. Check error handling in simulation loop")
    else:
        print(f"❌ SPICE simulation issues detected")
        print(f"💡 Recommendations:")
        print(f"   1. Check SPICE model syntax")
        print(f"   2. Verify circuit netlist generation")
        print(f"   3. Check ngspice installation and configuration")
        print(f"   4. Review parameter values for physical reasonableness")
    
    return True


if __name__ == "__main__":
    debug_idvg_idvd()
