#!/usr/bin/env python3
"""
Debug Negative R² Values

This script analyzes why we're getting negative R² values and fixes the issues.
"""

import sys
from pathlib import Path
import numpy as np
import matplotlib.pyplot as plt

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.mosfet_fitter.optimization.parameter_fitter import ParameterFitter


def debug_negative_r2():
    """Debug and analyze negative R² values."""
    
    print("🔍 DEBUGGING NEGATIVE R² VALUES")
    print("=" * 50)
    
    # Initialize fitter
    fitter = ParameterFitter("raw_data", "output/models/debug_model.lib")
    
    # Get default parameters
    all_params = fitter.model_template.get_all_fittable_parameters()
    default_parameters = {}
    for name, param in all_params.items():
        default_parameters[name] = param.initial_value
    
    print("📊 Analyzing each characteristic:")
    print("-" * 40)
    
    for char_name, measurement in fitter.measurement_data.items():
        if measurement is None or len(measurement.x_data) == 0:
            continue
            
        print(f"\n🧪 {char_name}:")
        print(f"   Measurement points: {len(measurement.x_data)}")
        print(f"   X range: {np.min(measurement.x_data):.3e} to {np.max(measurement.x_data):.3e}")
        print(f"   Y range: {np.min(measurement.y_data):.3e} to {np.max(measurement.y_data):.3e}")
        
        # Check for problematic values
        negative_count = np.sum(measurement.y_data < 0)
        zero_count = np.sum(measurement.y_data == 0)
        small_count = np.sum(np.abs(measurement.y_data) < 1e-12)
        
        if negative_count > 0:
            print(f"   ⚠️  {negative_count} negative values")
        if zero_count > 0:
            print(f"   ⚠️  {zero_count} zero values")
        if small_count > 0:
            print(f"   ⚠️  {small_count} very small values (< 1e-12)")
        
        # Try simulation
        try:
            sim_result = fitter.simulate_characteristic(default_parameters, char_name)
            
            if sim_result is not None:
                sim_x, sim_y = sim_result
                print(f"   ✅ Simulation successful: {len(sim_x)} points")
                print(f"   Sim X range: {np.min(sim_x):.3e} to {np.max(sim_x):.3e}")
                print(f"   Sim Y range: {np.min(sim_y):.3e} to {np.max(sim_y):.3e}")
                
                # Check simulation issues
                sim_negative = np.sum(sim_y < 0)
                sim_zero = np.sum(sim_y == 0)
                sim_nan = np.sum(np.isnan(sim_y))
                sim_inf = np.sum(np.isinf(sim_y))
                
                if sim_negative > 0:
                    print(f"   ⚠️  Simulation: {sim_negative} negative values")
                if sim_zero > 0:
                    print(f"   ⚠️  Simulation: {sim_zero} zero values")
                if sim_nan > 0:
                    print(f"   ❌ Simulation: {sim_nan} NaN values")
                if sim_inf > 0:
                    print(f"   ❌ Simulation: {sim_inf} Inf values")
                
                # Calculate R² manually to understand the issue
                if len(sim_x) > 1 and len(measurement.x_data) > 1:
                    try:
                        # Interpolate simulation to measurement points
                        sim_y_interp = np.interp(measurement.x_data, sim_x, sim_y)
                        
                        # Calculate R² components
                        ss_res = np.sum((measurement.y_data - sim_y_interp) ** 2)
                        ss_tot = np.sum((measurement.y_data - np.mean(measurement.y_data)) ** 2)
                        
                        if ss_tot > 0:
                            r_squared = 1 - (ss_res / ss_tot)
                            print(f"   📊 R² = {r_squared:.4f}")
                            print(f"   📊 SS_res = {ss_res:.3e}")
                            print(f"   📊 SS_tot = {ss_tot:.3e}")
                            print(f"   📊 Mean measured = {np.mean(measurement.y_data):.3e}")
                            print(f"   📊 Mean simulated = {np.mean(sim_y_interp):.3e}")
                            
                            if r_squared < 0:
                                print(f"   ❌ NEGATIVE R²! Simulation is worse than mean")
                                print(f"   💡 Residual sum > Total sum: fitting is very poor")
                                
                                # Show some example points
                                print(f"   📋 First 5 comparison points:")
                                for i in range(min(5, len(measurement.x_data))):
                                    x_val = measurement.x_data[i]
                                    meas_val = measurement.y_data[i]
                                    sim_val = sim_y_interp[i]
                                    error = abs(meas_val - sim_val)
                                    print(f"      X={x_val:.3e}: Meas={meas_val:.3e}, Sim={sim_val:.3e}, Error={error:.3e}")
                        else:
                            print(f"   ❌ SS_tot = 0, cannot calculate R²")
                            
                    except Exception as e:
                        print(f"   ❌ R² calculation error: {e}")
                
            else:
                print(f"   ❌ Simulation failed")
                
        except Exception as e:
            print(f"   ❌ Simulation error: {e}")
    
    print(f"\n💡 ANALYSIS AND RECOMMENDATIONS:")
    print("-" * 50)
    
    print(f"🔍 Common causes of negative R²:")
    print(f"   1. 仿真结果与测量数据完全不匹配")
    print(f"   2. 参数初始值不合理")
    print(f"   3. 仿真电路设置错误")
    print(f"   4. 数据预处理问题")
    print(f"   5. 插值方法不当")
    
    print(f"\n🔧 Recommended fixes:")
    print(f"   1. 改进参数初始值估计")
    print(f"   2. 调整仿真电路参数范围")
    print(f"   3. 使用更好的数据预处理")
    print(f"   4. 改进插值和匹配方法")
    print(f"   5. 添加物理约束")
    
    return True


if __name__ == "__main__":
    debug_negative_r2()
