* BVDSS Test Circuit - Breakdown Voltage Drain-to-Source
* Fixed Vg = 0.0V, Sweep Vd from 0.0V to 50.0V

.include output/models/fitted_model.lib

* Test circuit
VGS gate 0 DC 0.0
VDS drain 0 DC 0
VSS source 0 DC 0

* Device under test
X1 drain gate source SWA04K006

* Current measurement through drain
.probe I(VDS)

* DC sweep analysis
.dc VDS 0.0 50.0 0.5
.print dc v(drain) I(VDS)

.control
run
print all
.endc

.end