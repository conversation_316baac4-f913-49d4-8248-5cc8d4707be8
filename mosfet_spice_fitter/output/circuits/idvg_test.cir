* IdVg Test Circuit - Drain Current vs Gate Voltage
* Fixed Vd = 5.0V, Sweep Vg from 0.0V to 10.0V

.include output/models/fitted_model.lib

* Test circuit
VGS gate 0 DC 0
VDS drain 0 DC 5.0
VSS source 0 DC 0

* Device under test
X1 drain gate source SWA04K006

* Current measurement through drain
.probe I(VDS)

* DC sweep analysis
.dc VGS 0.0 10.0 0.1
.print dc v(gate) I(VDS)

.control
run
print all
.endc

.end