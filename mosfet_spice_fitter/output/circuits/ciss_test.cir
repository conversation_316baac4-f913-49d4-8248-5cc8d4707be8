* Ciss Test Circuit - Input Capacitance (Cgs + Cgd)
* Fixed Vd = 25.0V, Sweep Vg from 0.0V to 10.0V

.include output/models/fitted_model.lib

* Test circuit with AC source for capacitance measurement
VGS gate 0 DC 0
VAC gate_ac gate AC 1 0
VDS drain 0 DC 25.0
VSS source 0 DC 0

* Device under test
X1 drain gate source SWA04K006

* Small signal AC analysis for capacitance
.ac lin 1 1000000.0 1000000.0

* DC sweep for bias point
.dc VGS 0.0 10.0 0.1

* Calculate input capacitance (Cgs + Cgd)
.print dc v(gate) @X1[cgs] @X1[cgd]

.control
run
print all
.endc

.end