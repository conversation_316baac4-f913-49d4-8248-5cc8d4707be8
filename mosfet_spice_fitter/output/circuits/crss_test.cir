* Crss Test Circuit - Reverse Transfer Capacitance (Cgd)
* Fixed Vg = 0.0V, Sweep Vd from 0.0V to 40.0V

.include output/models/fitted_model.lib

* Test circuit with AC source for capacitance measurement
VGS gate 0 DC 0.0
VDS drain 0 DC 0
VAC drain_ac drain AC 1 0
VSS source 0 DC 0

* Device under test
X1 drain gate source SWA04K006

* Small signal AC analysis for capacitance
.ac lin 1 1000000.0 1000000.0

* DC sweep for bias point
.dc VDS 0.0 40.0 0.4

* Calculate reverse transfer capacitance (Cgd)
.print dc v(drain) @X1[cgd]

.control
run
print all
.endc

.end