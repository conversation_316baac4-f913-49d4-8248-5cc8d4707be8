2025-07-14 16:43:20,419 - main - INFO - Starting MOSFET SPICE Parameter Fitting Workflow
2025-07-14 16:43:20,420 - main - INFO - ============================================================
2025-07-14 16:43:20,424 - main - INFO - ngspice is available and working
2025-07-14 16:43:20,424 - main - INFO - Raw data directory found
2025-07-14 16:43:20,424 - main - INFO - Step 1: Initializing components...
2025-07-14 16:43:20,430 - parameter_fitter - INFO - Loaded measurement data for: ['IdVg', 'BVDSS', 'Cgs', 'Cds', 'Cgd', 'IdVd']
2025-07-14 16:43:20,430 - main - INFO - All components initialized successfully
2025-07-14 16:43:20,430 - main - INFO - Step 2: Loading and analyzing measurement data...
2025-07-14 16:43:20,433 - main - INFO - Loaded electrical data for: ['IdVg', 'IdVd', 'BVDSS', 'Capacitance', 'VFD']
2025-07-14 16:43:20,435 - main - INFO - Analyzed SPICE library: ['SWA04K006']
2025-07-14 16:43:20,435 - main - INFO - Step 3: Generating test circuits...
2025-07-14 16:43:20,436 - main - INFO - Test circuits generated and saved
2025-07-14 16:43:20,436 - main - INFO - Step 4: Running parameter fitting...
2025-07-14 16:43:20,436 - main - INFO - This may take several minutes depending on the number of parameters...
2025-07-14 16:43:20,436 - parameter_fitter - INFO - Starting parameter fitting with 3 parameters
2025-07-14 16:43:20,436 - parameter_fitter - INFO - Parameters to fit: ['Vto', 'param_Cgs_0', 'param_Cds_0']
2025-07-14 16:43:20,646 - parameter_fitter - INFO - Fitting completed in 0.21 seconds
2025-07-14 16:43:20,647 - parameter_fitter - INFO - Success: True, Final cost: 8.300002e+13
2025-07-14 16:43:20,647 - main - INFO - Parameter fitting completed successfully!
2025-07-14 16:43:20,647 - main - INFO - Final cost: 8.300002e+13
2025-07-14 16:43:20,647 - main - INFO - Iterations: 2
2025-07-14 16:43:20,647 - main - INFO - Time: 0.21 seconds
2025-07-14 16:43:20,647 - main - INFO - Step 5: Generating fitted SPICE model...
2025-07-14 16:43:20,647 - parameter_fitter - INFO - Fitted model saved to output/models/fitted_model.lib
2025-07-14 16:43:20,647 - main - INFO - Fitted SPICE model saved
2025-07-14 16:43:20,647 - main - INFO - Step 6: Generating comparison plots and reports...
2025-07-14 16:43:20,727 - matplotlib.pyplot - DEBUG - Loaded backend tkagg version 8.6.
2025-07-14 16:43:20,840 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-07-14 16:43:20,840 - PIL.PngImagePlugin - DEBUG - STREAM b'sBIT' 41 4
2025-07-14 16:43:20,841 - PIL.PngImagePlugin - DEBUG - b'sBIT' 41 4 (unknown)
2025-07-14 16:43:20,841 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 57 9
2025-07-14 16:43:20,841 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 78 1189
2025-07-14 16:43:20,845 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-07-14 16:43:20,845 - PIL.PngImagePlugin - DEBUG - STREAM b'sBIT' 41 4
2025-07-14 16:43:20,845 - PIL.PngImagePlugin - DEBUG - b'sBIT' 41 4 (unknown)
2025-07-14 16:43:20,845 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 57 9
2025-07-14 16:43:20,845 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 78 2994
2025-07-14 16:43:20,903 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-07-14 16:43:20,904 - PIL.PngImagePlugin - DEBUG - STREAM b'sBIT' 41 4
2025-07-14 16:43:20,904 - PIL.PngImagePlugin - DEBUG - b'sBIT' 41 4 (unknown)
2025-07-14 16:43:20,904 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 57 9
2025-07-14 16:43:20,904 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 78 374
2025-07-14 16:43:20,907 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-07-14 16:43:20,907 - PIL.PngImagePlugin - DEBUG - STREAM b'sBIT' 41 4
2025-07-14 16:43:20,907 - PIL.PngImagePlugin - DEBUG - b'sBIT' 41 4 (unknown)
2025-07-14 16:43:20,907 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 57 9
2025-07-14 16:43:20,908 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 78 286
2025-07-14 16:43:20,908 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-07-14 16:43:20,909 - PIL.PngImagePlugin - DEBUG - STREAM b'sBIT' 41 4
2025-07-14 16:43:20,909 - PIL.PngImagePlugin - DEBUG - b'sBIT' 41 4 (unknown)
2025-07-14 16:43:20,909 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 57 9
2025-07-14 16:43:20,909 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 78 263
2025-07-14 16:43:20,911 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-07-14 16:43:20,911 - PIL.PngImagePlugin - DEBUG - STREAM b'sBIT' 41 4
2025-07-14 16:43:20,911 - PIL.PngImagePlugin - DEBUG - b'sBIT' 41 4 (unknown)
2025-07-14 16:43:20,911 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 57 9
2025-07-14 16:43:20,911 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 78 387
2025-07-14 16:43:20,912 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-07-14 16:43:20,912 - PIL.PngImagePlugin - DEBUG - STREAM b'sBIT' 41 4
2025-07-14 16:43:20,912 - PIL.PngImagePlugin - DEBUG - b'sBIT' 41 4 (unknown)
2025-07-14 16:43:20,912 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 57 9
2025-07-14 16:43:20,912 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 78 436
2025-07-14 16:43:20,914 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-07-14 16:43:20,914 - PIL.PngImagePlugin - DEBUG - STREAM b'sBIT' 41 4
2025-07-14 16:43:20,914 - PIL.PngImagePlugin - DEBUG - b'sBIT' 41 4 (unknown)
2025-07-14 16:43:20,914 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 57 9
2025-07-14 16:43:20,914 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 78 351
2025-07-14 16:43:20,915 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-07-14 16:43:20,915 - PIL.PngImagePlugin - DEBUG - STREAM b'sBIT' 41 4
2025-07-14 16:43:20,915 - PIL.PngImagePlugin - DEBUG - b'sBIT' 41 4 (unknown)
2025-07-14 16:43:20,915 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 57 9
2025-07-14 16:43:20,915 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 78 364
2025-07-14 16:43:20,919 - matplotlib.font_manager - DEBUG - findfont: Matching sans\-serif:style=normal:variant=normal:weight=normal:stretch=normal:size=10.0.
2025-07-14 16:43:20,919 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf', name='DejaVu Sans', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 0.05
2025-07-14 16:43:20,920 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizTwoSymBol.ttf', name='STIXSizeTwoSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-14 16:43:20,920 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizFourSymBol.ttf', name='STIXSizeFourSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-14 16:43:20,920 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-Oblique.ttf', name='DejaVu Sans Mono', style='oblique', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:20,920 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/cmb10.ttf', name='cmb10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:20,920 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizOneSymReg.ttf', name='STIXSizeOneSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:20,920 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizFiveSymReg.ttf', name='STIXSizeFiveSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:20,920 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/cmmi10.ttf', name='cmmi10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:20,920 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSerif-BoldItalic.ttf', name='DejaVu Serif', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-14 16:43:20,921 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSansDisplay.ttf', name='DejaVu Sans Display', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:20,921 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizFourSymReg.ttf', name='STIXSizeFourSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:20,921 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-Bold.ttf', name='DejaVu Sans Mono', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-14 16:43:20,921 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSerif.ttf', name='DejaVu Serif', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:20,921 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizThreeSymReg.ttf', name='STIXSizeThreeSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:20,921 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSerifDisplay.ttf', name='DejaVu Serif Display', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:20,921 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans-Bold.ttf', name='DejaVu Sans', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 0.33499999999999996
2025-07-14 16:43:20,921 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXNonUniBol.ttf', name='STIXNonUnicode', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-14 16:43:20,921 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/cmr10.ttf', name='cmr10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:20,921 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/cmex10.ttf', name='cmex10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:20,921 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/cmsy10.ttf', name='cmsy10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:20,921 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/cmss10.ttf', name='cmss10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:20,921 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSerif-Bold.ttf', name='DejaVu Serif', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-14 16:43:20,921 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizThreeSymBol.ttf', name='STIXSizeThreeSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-14 16:43:20,921 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizTwoSymReg.ttf', name='STIXSizeTwoSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:20,922 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXGeneralBolIta.ttf', name='STIXGeneral', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-14 16:43:20,922 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSansMono.ttf', name='DejaVu Sans Mono', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:20,922 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/cmtt10.ttf', name='cmtt10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:20,922 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSerif-Italic.ttf', name='DejaVu Serif', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:20,922 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans-BoldOblique.ttf', name='DejaVu Sans', style='oblique', variant='normal', weight=700, stretch='normal', size='scalable')) = 1.335
2025-07-14 16:43:20,922 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXGeneralBol.ttf', name='STIXGeneral', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-14 16:43:20,922 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-BoldOblique.ttf', name='DejaVu Sans Mono', style='oblique', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-14 16:43:20,922 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXNonUniBolIta.ttf', name='STIXNonUnicode', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-14 16:43:20,922 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizOneSymBol.ttf', name='STIXSizeOneSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-14 16:43:20,923 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans-Oblique.ttf', name='DejaVu Sans', style='oblique', variant='normal', weight=400, stretch='normal', size='scalable')) = 1.05
2025-07-14 16:43:20,923 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXNonUniIta.ttf', name='STIXNonUnicode', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:20,923 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXGeneral.ttf', name='STIXGeneral', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:20,923 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXNonUni.ttf', name='STIXNonUnicode', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:20,923 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXGeneralItalic.ttf', name='STIXGeneral', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:20,923 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/Ubuntu-C.ttf', name='Ubuntu', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:20,923 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/Ubuntu-L.ttf', name='Ubuntu', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:20,923 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/Ubuntu-MI.ttf', name='Ubuntu', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:20,923 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/Ubuntu-LI.ttf', name='Ubuntu', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:20,923 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/UbuntuSansMono-Italic[wght].ttf', name='Ubuntu Sans Mono', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:20,923 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/dejavu/DejaVuSerif.ttf', name='DejaVu Serif', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:20,923 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/dejavu/DejaVuSansMono-Bold.ttf', name='DejaVu Sans Mono', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-14 16:43:20,923 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/UbuntuMono-R.ttf', name='Ubuntu Mono', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:20,924 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/Ubuntu-M.ttf', name='Ubuntu', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:20,924 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/UbuntuSans[wdth,wght].ttf', name='Ubuntu Sans', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:20,924 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/Ubuntu-Italic[wdth,wght].ttf', name='Ubuntu', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:20,924 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/Ubuntu[wdth,wght].ttf', name='Ubuntu', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:20,924 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/UbuntuMono[wght].ttf', name='Ubuntu Mono', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:20,924 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/UbuntuMono-RI.ttf', name='Ubuntu Mono', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:20,924 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/UbuntuMono-B.ttf', name='Ubuntu Mono', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:20,924 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/UbuntuMono-BI.ttf', name='Ubuntu Mono', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:20,924 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/Ubuntu-RI.ttf', name='Ubuntu', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:20,924 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/UbuntuMono-Italic[wght].ttf', name='Ubuntu Mono', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:20,924 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/UbuntuSans-Italic[wdth,wght].ttf', name='Ubuntu Sans', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:20,924 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/UbuntuSansMono[wght].ttf', name='Ubuntu Sans Mono', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:20,924 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/dejavu/DejaVuSansMono-Oblique.ttf', name='DejaVu Sans Mono', style='oblique', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:20,924 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/Ubuntu-BI.ttf', name='Ubuntu', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:20,925 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/Ubuntu-Th.ttf', name='Ubuntu', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:20,925 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/dejavu/DejaVuSansMono-BoldOblique.ttf', name='DejaVu Sans Mono', style='oblique', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-14 16:43:20,925 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/dejavu/DejaVuSansMono.ttf', name='DejaVu Sans Mono', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:20,925 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf', name='DejaVu Sans', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 0.05
2025-07-14 16:43:20,925 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/Ubuntu-R.ttf', name='Ubuntu', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:20,925 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf', name='DejaVu Sans', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 0.33499999999999996
2025-07-14 16:43:20,925 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/Ubuntu-B.ttf', name='Ubuntu', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:20,925 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/dejavu/DejaVuSerif-Bold.ttf', name='DejaVu Serif', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-14 16:43:20,925 - matplotlib.font_manager - DEBUG - findfont: Matching sans\-serif:style=normal:variant=normal:weight=normal:stretch=normal:size=10.0 to DejaVu Sans ('/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf') with score of 0.050000.
2025-07-14 16:43:20,984 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-07-14 16:43:20,984 - PIL.PngImagePlugin - DEBUG - STREAM b'sBIT' 41 4
2025-07-14 16:43:20,984 - PIL.PngImagePlugin - DEBUG - b'sBIT' 41 4 (unknown)
2025-07-14 16:43:20,984 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 57 9
2025-07-14 16:43:20,985 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 78 1189
2025-07-14 16:43:20,987 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-07-14 16:43:20,987 - PIL.PngImagePlugin - DEBUG - STREAM b'sBIT' 41 4
2025-07-14 16:43:20,988 - PIL.PngImagePlugin - DEBUG - b'sBIT' 41 4 (unknown)
2025-07-14 16:43:20,988 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 57 9
2025-07-14 16:43:20,988 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 78 2994
2025-07-14 16:43:20,993 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-07-14 16:43:20,993 - PIL.PngImagePlugin - DEBUG - STREAM b'sBIT' 41 4
2025-07-14 16:43:20,993 - PIL.PngImagePlugin - DEBUG - b'sBIT' 41 4 (unknown)
2025-07-14 16:43:20,993 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 57 9
2025-07-14 16:43:20,993 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 78 374
2025-07-14 16:43:20,996 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-07-14 16:43:20,996 - PIL.PngImagePlugin - DEBUG - STREAM b'sBIT' 41 4
2025-07-14 16:43:20,996 - PIL.PngImagePlugin - DEBUG - b'sBIT' 41 4 (unknown)
2025-07-14 16:43:20,996 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 57 9
2025-07-14 16:43:20,996 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 78 286
2025-07-14 16:43:20,997 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-07-14 16:43:20,997 - PIL.PngImagePlugin - DEBUG - STREAM b'sBIT' 41 4
2025-07-14 16:43:20,997 - PIL.PngImagePlugin - DEBUG - b'sBIT' 41 4 (unknown)
2025-07-14 16:43:20,998 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 57 9
2025-07-14 16:43:20,998 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 78 263
2025-07-14 16:43:20,999 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-07-14 16:43:20,999 - PIL.PngImagePlugin - DEBUG - STREAM b'sBIT' 41 4
2025-07-14 16:43:20,999 - PIL.PngImagePlugin - DEBUG - b'sBIT' 41 4 (unknown)
2025-07-14 16:43:20,999 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 57 9
2025-07-14 16:43:20,999 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 78 387
2025-07-14 16:43:21,001 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-07-14 16:43:21,001 - PIL.PngImagePlugin - DEBUG - STREAM b'sBIT' 41 4
2025-07-14 16:43:21,001 - PIL.PngImagePlugin - DEBUG - b'sBIT' 41 4 (unknown)
2025-07-14 16:43:21,001 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 57 9
2025-07-14 16:43:21,001 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 78 436
2025-07-14 16:43:21,002 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-07-14 16:43:21,003 - PIL.PngImagePlugin - DEBUG - STREAM b'sBIT' 41 4
2025-07-14 16:43:21,003 - PIL.PngImagePlugin - DEBUG - b'sBIT' 41 4 (unknown)
2025-07-14 16:43:21,003 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 57 9
2025-07-14 16:43:21,003 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 78 351
2025-07-14 16:43:21,004 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-07-14 16:43:21,004 - PIL.PngImagePlugin - DEBUG - STREAM b'sBIT' 41 4
2025-07-14 16:43:21,005 - PIL.PngImagePlugin - DEBUG - b'sBIT' 41 4 (unknown)
2025-07-14 16:43:21,005 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 57 9
2025-07-14 16:43:21,005 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 78 364
2025-07-14 16:43:21,077 - matplotlib.font_manager - DEBUG - findfont: Matching sans\-serif:style=normal:variant=normal:weight=normal:stretch=normal:size=12.0.
2025-07-14 16:43:21,077 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf', name='DejaVu Sans', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 0.05
2025-07-14 16:43:21,077 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizTwoSymBol.ttf', name='STIXSizeTwoSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-14 16:43:21,077 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizFourSymBol.ttf', name='STIXSizeFourSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-14 16:43:21,077 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-Oblique.ttf', name='DejaVu Sans Mono', style='oblique', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:21,077 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/cmb10.ttf', name='cmb10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:21,077 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizOneSymReg.ttf', name='STIXSizeOneSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:21,077 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizFiveSymReg.ttf', name='STIXSizeFiveSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:21,077 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/cmmi10.ttf', name='cmmi10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:21,077 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSerif-BoldItalic.ttf', name='DejaVu Serif', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-14 16:43:21,078 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSansDisplay.ttf', name='DejaVu Sans Display', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:21,078 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizFourSymReg.ttf', name='STIXSizeFourSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:21,078 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-Bold.ttf', name='DejaVu Sans Mono', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-14 16:43:21,078 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSerif.ttf', name='DejaVu Serif', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:21,078 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizThreeSymReg.ttf', name='STIXSizeThreeSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:21,078 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSerifDisplay.ttf', name='DejaVu Serif Display', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:21,078 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans-Bold.ttf', name='DejaVu Sans', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 0.33499999999999996
2025-07-14 16:43:21,078 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXNonUniBol.ttf', name='STIXNonUnicode', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-14 16:43:21,078 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/cmr10.ttf', name='cmr10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:21,078 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/cmex10.ttf', name='cmex10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:21,078 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/cmsy10.ttf', name='cmsy10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:21,078 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/cmss10.ttf', name='cmss10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:21,079 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSerif-Bold.ttf', name='DejaVu Serif', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-14 16:43:21,079 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizThreeSymBol.ttf', name='STIXSizeThreeSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-14 16:43:21,079 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizTwoSymReg.ttf', name='STIXSizeTwoSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:21,079 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXGeneralBolIta.ttf', name='STIXGeneral', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-14 16:43:21,079 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSansMono.ttf', name='DejaVu Sans Mono', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:21,079 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/cmtt10.ttf', name='cmtt10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:21,079 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSerif-Italic.ttf', name='DejaVu Serif', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:21,079 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans-BoldOblique.ttf', name='DejaVu Sans', style='oblique', variant='normal', weight=700, stretch='normal', size='scalable')) = 1.335
2025-07-14 16:43:21,079 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXGeneralBol.ttf', name='STIXGeneral', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-14 16:43:21,079 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-BoldOblique.ttf', name='DejaVu Sans Mono', style='oblique', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-14 16:43:21,080 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXNonUniBolIta.ttf', name='STIXNonUnicode', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-14 16:43:21,080 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizOneSymBol.ttf', name='STIXSizeOneSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-14 16:43:21,080 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans-Oblique.ttf', name='DejaVu Sans', style='oblique', variant='normal', weight=400, stretch='normal', size='scalable')) = 1.05
2025-07-14 16:43:21,080 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXNonUniIta.ttf', name='STIXNonUnicode', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:21,080 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXGeneral.ttf', name='STIXGeneral', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:21,080 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXNonUni.ttf', name='STIXNonUnicode', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:21,080 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXGeneralItalic.ttf', name='STIXGeneral', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:21,080 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/Ubuntu-C.ttf', name='Ubuntu', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:21,080 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/Ubuntu-L.ttf', name='Ubuntu', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:21,080 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/Ubuntu-MI.ttf', name='Ubuntu', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:21,080 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/Ubuntu-LI.ttf', name='Ubuntu', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:21,080 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/UbuntuSansMono-Italic[wght].ttf', name='Ubuntu Sans Mono', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:21,081 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/dejavu/DejaVuSerif.ttf', name='DejaVu Serif', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:21,081 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/dejavu/DejaVuSansMono-Bold.ttf', name='DejaVu Sans Mono', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-14 16:43:21,081 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/UbuntuMono-R.ttf', name='Ubuntu Mono', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:21,081 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/Ubuntu-M.ttf', name='Ubuntu', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:21,081 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/UbuntuSans[wdth,wght].ttf', name='Ubuntu Sans', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:21,081 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/Ubuntu-Italic[wdth,wght].ttf', name='Ubuntu', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:21,081 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/Ubuntu[wdth,wght].ttf', name='Ubuntu', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:21,081 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/UbuntuMono[wght].ttf', name='Ubuntu Mono', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:21,081 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/UbuntuMono-RI.ttf', name='Ubuntu Mono', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:21,081 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/UbuntuMono-B.ttf', name='Ubuntu Mono', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:21,081 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/UbuntuMono-BI.ttf', name='Ubuntu Mono', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:21,081 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/Ubuntu-RI.ttf', name='Ubuntu', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:21,081 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/UbuntuMono-Italic[wght].ttf', name='Ubuntu Mono', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:21,081 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/UbuntuSans-Italic[wdth,wght].ttf', name='Ubuntu Sans', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:21,082 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/UbuntuSansMono[wght].ttf', name='Ubuntu Sans Mono', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:21,082 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/dejavu/DejaVuSansMono-Oblique.ttf', name='DejaVu Sans Mono', style='oblique', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:21,082 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/Ubuntu-BI.ttf', name='Ubuntu', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:21,082 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/Ubuntu-Th.ttf', name='Ubuntu', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:21,082 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/dejavu/DejaVuSansMono-BoldOblique.ttf', name='DejaVu Sans Mono', style='oblique', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-14 16:43:21,082 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/dejavu/DejaVuSansMono.ttf', name='DejaVu Sans Mono', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:21,082 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf', name='DejaVu Sans', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 0.05
2025-07-14 16:43:21,082 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/Ubuntu-R.ttf', name='Ubuntu', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:21,082 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf', name='DejaVu Sans', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 0.33499999999999996
2025-07-14 16:43:21,082 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/Ubuntu-B.ttf', name='Ubuntu', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:21,082 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/dejavu/DejaVuSerif-Bold.ttf', name='DejaVu Serif', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-14 16:43:21,082 - matplotlib.font_manager - DEBUG - findfont: Matching sans\-serif:style=normal:variant=normal:weight=normal:stretch=normal:size=12.0 to DejaVu Sans ('/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf') with score of 0.050000.
2025-07-14 16:43:21,386 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-07-14 16:43:21,386 - PIL.PngImagePlugin - DEBUG - STREAM b'sBIT' 41 4
2025-07-14 16:43:21,386 - PIL.PngImagePlugin - DEBUG - b'sBIT' 41 4 (unknown)
2025-07-14 16:43:21,386 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 57 9
2025-07-14 16:43:21,386 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 78 1189
2025-07-14 16:43:21,389 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-07-14 16:43:21,390 - PIL.PngImagePlugin - DEBUG - STREAM b'sBIT' 41 4
2025-07-14 16:43:21,390 - PIL.PngImagePlugin - DEBUG - b'sBIT' 41 4 (unknown)
2025-07-14 16:43:21,390 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 57 9
2025-07-14 16:43:21,390 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 78 2994
2025-07-14 16:43:21,396 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-07-14 16:43:21,396 - PIL.PngImagePlugin - DEBUG - STREAM b'sBIT' 41 4
2025-07-14 16:43:21,397 - PIL.PngImagePlugin - DEBUG - b'sBIT' 41 4 (unknown)
2025-07-14 16:43:21,397 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 57 9
2025-07-14 16:43:21,397 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 78 374
2025-07-14 16:43:21,398 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-07-14 16:43:21,398 - PIL.PngImagePlugin - DEBUG - STREAM b'sBIT' 41 4
2025-07-14 16:43:21,398 - PIL.PngImagePlugin - DEBUG - b'sBIT' 41 4 (unknown)
2025-07-14 16:43:21,398 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 57 9
2025-07-14 16:43:21,398 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 78 286
2025-07-14 16:43:21,399 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-07-14 16:43:21,399 - PIL.PngImagePlugin - DEBUG - STREAM b'sBIT' 41 4
2025-07-14 16:43:21,400 - PIL.PngImagePlugin - DEBUG - b'sBIT' 41 4 (unknown)
2025-07-14 16:43:21,400 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 57 9
2025-07-14 16:43:21,400 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 78 263
2025-07-14 16:43:21,401 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-07-14 16:43:21,401 - PIL.PngImagePlugin - DEBUG - STREAM b'sBIT' 41 4
2025-07-14 16:43:21,401 - PIL.PngImagePlugin - DEBUG - b'sBIT' 41 4 (unknown)
2025-07-14 16:43:21,401 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 57 9
2025-07-14 16:43:21,401 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 78 387
2025-07-14 16:43:21,403 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-07-14 16:43:21,403 - PIL.PngImagePlugin - DEBUG - STREAM b'sBIT' 41 4
2025-07-14 16:43:21,403 - PIL.PngImagePlugin - DEBUG - b'sBIT' 41 4 (unknown)
2025-07-14 16:43:21,404 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 57 9
2025-07-14 16:43:21,404 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 78 436
2025-07-14 16:43:21,405 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-07-14 16:43:21,405 - PIL.PngImagePlugin - DEBUG - STREAM b'sBIT' 41 4
2025-07-14 16:43:21,406 - PIL.PngImagePlugin - DEBUG - b'sBIT' 41 4 (unknown)
2025-07-14 16:43:21,406 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 57 9
2025-07-14 16:43:21,406 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 78 351
2025-07-14 16:43:21,407 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-07-14 16:43:21,407 - PIL.PngImagePlugin - DEBUG - STREAM b'sBIT' 41 4
2025-07-14 16:43:21,407 - PIL.PngImagePlugin - DEBUG - b'sBIT' 41 4 (unknown)
2025-07-14 16:43:21,407 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 57 9
2025-07-14 16:43:21,407 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 78 364
2025-07-14 16:43:21,743 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-07-14 16:43:21,744 - PIL.PngImagePlugin - DEBUG - STREAM b'sBIT' 41 4
2025-07-14 16:43:21,744 - PIL.PngImagePlugin - DEBUG - b'sBIT' 41 4 (unknown)
2025-07-14 16:43:21,744 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 57 9
2025-07-14 16:43:21,744 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 78 1189
2025-07-14 16:43:21,746 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-07-14 16:43:21,747 - PIL.PngImagePlugin - DEBUG - STREAM b'sBIT' 41 4
2025-07-14 16:43:21,747 - PIL.PngImagePlugin - DEBUG - b'sBIT' 41 4 (unknown)
2025-07-14 16:43:21,747 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 57 9
2025-07-14 16:43:21,747 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 78 2994
2025-07-14 16:43:21,750 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-07-14 16:43:21,750 - PIL.PngImagePlugin - DEBUG - STREAM b'sBIT' 41 4
2025-07-14 16:43:21,750 - PIL.PngImagePlugin - DEBUG - b'sBIT' 41 4 (unknown)
2025-07-14 16:43:21,750 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 57 9
2025-07-14 16:43:21,750 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 78 374
2025-07-14 16:43:21,751 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-07-14 16:43:21,752 - PIL.PngImagePlugin - DEBUG - STREAM b'sBIT' 41 4
2025-07-14 16:43:21,752 - PIL.PngImagePlugin - DEBUG - b'sBIT' 41 4 (unknown)
2025-07-14 16:43:21,752 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 57 9
2025-07-14 16:43:21,752 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 78 286
2025-07-14 16:43:21,753 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-07-14 16:43:21,754 - PIL.PngImagePlugin - DEBUG - STREAM b'sBIT' 41 4
2025-07-14 16:43:21,754 - PIL.PngImagePlugin - DEBUG - b'sBIT' 41 4 (unknown)
2025-07-14 16:43:21,754 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 57 9
2025-07-14 16:43:21,754 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 78 263
2025-07-14 16:43:21,755 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-07-14 16:43:21,755 - PIL.PngImagePlugin - DEBUG - STREAM b'sBIT' 41 4
2025-07-14 16:43:21,755 - PIL.PngImagePlugin - DEBUG - b'sBIT' 41 4 (unknown)
2025-07-14 16:43:21,755 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 57 9
2025-07-14 16:43:21,755 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 78 387
2025-07-14 16:43:21,756 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-07-14 16:43:21,756 - PIL.PngImagePlugin - DEBUG - STREAM b'sBIT' 41 4
2025-07-14 16:43:21,756 - PIL.PngImagePlugin - DEBUG - b'sBIT' 41 4 (unknown)
2025-07-14 16:43:21,756 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 57 9
2025-07-14 16:43:21,756 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 78 436
2025-07-14 16:43:21,758 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-07-14 16:43:21,758 - PIL.PngImagePlugin - DEBUG - STREAM b'sBIT' 41 4
2025-07-14 16:43:21,758 - PIL.PngImagePlugin - DEBUG - b'sBIT' 41 4 (unknown)
2025-07-14 16:43:21,758 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 57 9
2025-07-14 16:43:21,758 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 78 351
2025-07-14 16:43:21,759 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-07-14 16:43:21,759 - PIL.PngImagePlugin - DEBUG - STREAM b'sBIT' 41 4
2025-07-14 16:43:21,759 - PIL.PngImagePlugin - DEBUG - b'sBIT' 41 4 (unknown)
2025-07-14 16:43:21,759 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 57 9
2025-07-14 16:43:21,759 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 78 364
2025-07-14 16:43:22,160 - matplotlib.font_manager - DEBUG - findfont: Matching sans\-serif:style=normal:variant=normal:weight=normal:stretch=normal:size=16.0.
2025-07-14 16:43:22,160 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf', name='DejaVu Sans', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 0.05
2025-07-14 16:43:22,161 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizTwoSymBol.ttf', name='STIXSizeTwoSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-14 16:43:22,161 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizFourSymBol.ttf', name='STIXSizeFourSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-14 16:43:22,161 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-Oblique.ttf', name='DejaVu Sans Mono', style='oblique', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:22,161 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/cmb10.ttf', name='cmb10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:22,161 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizOneSymReg.ttf', name='STIXSizeOneSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:22,161 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizFiveSymReg.ttf', name='STIXSizeFiveSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:22,161 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/cmmi10.ttf', name='cmmi10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:22,161 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSerif-BoldItalic.ttf', name='DejaVu Serif', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-14 16:43:22,161 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSansDisplay.ttf', name='DejaVu Sans Display', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:22,161 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizFourSymReg.ttf', name='STIXSizeFourSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:22,161 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-Bold.ttf', name='DejaVu Sans Mono', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-14 16:43:22,161 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSerif.ttf', name='DejaVu Serif', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:22,161 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizThreeSymReg.ttf', name='STIXSizeThreeSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:22,161 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSerifDisplay.ttf', name='DejaVu Serif Display', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:22,161 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans-Bold.ttf', name='DejaVu Sans', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 0.33499999999999996
2025-07-14 16:43:22,162 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXNonUniBol.ttf', name='STIXNonUnicode', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-14 16:43:22,162 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/cmr10.ttf', name='cmr10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:22,162 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/cmex10.ttf', name='cmex10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:22,162 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/cmsy10.ttf', name='cmsy10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:22,162 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/cmss10.ttf', name='cmss10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:22,162 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSerif-Bold.ttf', name='DejaVu Serif', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-14 16:43:22,162 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizThreeSymBol.ttf', name='STIXSizeThreeSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-14 16:43:22,162 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizTwoSymReg.ttf', name='STIXSizeTwoSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:22,162 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXGeneralBolIta.ttf', name='STIXGeneral', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-14 16:43:22,162 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSansMono.ttf', name='DejaVu Sans Mono', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:22,162 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/cmtt10.ttf', name='cmtt10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:22,163 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSerif-Italic.ttf', name='DejaVu Serif', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:22,163 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans-BoldOblique.ttf', name='DejaVu Sans', style='oblique', variant='normal', weight=700, stretch='normal', size='scalable')) = 1.335
2025-07-14 16:43:22,163 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXGeneralBol.ttf', name='STIXGeneral', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-14 16:43:22,163 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-BoldOblique.ttf', name='DejaVu Sans Mono', style='oblique', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-14 16:43:22,163 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXNonUniBolIta.ttf', name='STIXNonUnicode', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-14 16:43:22,163 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizOneSymBol.ttf', name='STIXSizeOneSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-14 16:43:22,163 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans-Oblique.ttf', name='DejaVu Sans', style='oblique', variant='normal', weight=400, stretch='normal', size='scalable')) = 1.05
2025-07-14 16:43:22,163 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXNonUniIta.ttf', name='STIXNonUnicode', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:22,163 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXGeneral.ttf', name='STIXGeneral', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:22,163 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXNonUni.ttf', name='STIXNonUnicode', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:22,164 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/STIXGeneralItalic.ttf', name='STIXGeneral', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:22,164 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/Ubuntu-C.ttf', name='Ubuntu', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:22,164 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/Ubuntu-L.ttf', name='Ubuntu', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:22,164 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/Ubuntu-MI.ttf', name='Ubuntu', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:22,164 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/Ubuntu-LI.ttf', name='Ubuntu', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:22,164 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/UbuntuSansMono-Italic[wght].ttf', name='Ubuntu Sans Mono', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:22,164 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/dejavu/DejaVuSerif.ttf', name='DejaVu Serif', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:22,164 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/dejavu/DejaVuSansMono-Bold.ttf', name='DejaVu Sans Mono', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-14 16:43:22,164 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/UbuntuMono-R.ttf', name='Ubuntu Mono', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:22,164 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/Ubuntu-M.ttf', name='Ubuntu', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:22,164 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/UbuntuSans[wdth,wght].ttf', name='Ubuntu Sans', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:22,164 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/Ubuntu-Italic[wdth,wght].ttf', name='Ubuntu', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:22,165 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/Ubuntu[wdth,wght].ttf', name='Ubuntu', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:22,165 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/UbuntuMono[wght].ttf', name='Ubuntu Mono', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:22,165 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/UbuntuMono-RI.ttf', name='Ubuntu Mono', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:22,165 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/UbuntuMono-B.ttf', name='Ubuntu Mono', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:22,165 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/UbuntuMono-BI.ttf', name='Ubuntu Mono', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:22,165 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/Ubuntu-RI.ttf', name='Ubuntu', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:22,165 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/UbuntuMono-Italic[wght].ttf', name='Ubuntu Mono', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:22,165 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/UbuntuSans-Italic[wdth,wght].ttf', name='Ubuntu Sans', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:22,165 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/UbuntuSansMono[wght].ttf', name='Ubuntu Sans Mono', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:22,165 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/dejavu/DejaVuSansMono-Oblique.ttf', name='DejaVu Sans Mono', style='oblique', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:22,165 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/Ubuntu-BI.ttf', name='Ubuntu', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-07-14 16:43:22,165 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/Ubuntu-Th.ttf', name='Ubuntu', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:22,165 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/dejavu/DejaVuSansMono-BoldOblique.ttf', name='DejaVu Sans Mono', style='oblique', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-07-14 16:43:22,165 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/dejavu/DejaVuSansMono.ttf', name='DejaVu Sans Mono', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:22,166 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf', name='DejaVu Sans', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 0.05
2025-07-14 16:43:22,166 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/Ubuntu-R.ttf', name='Ubuntu', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:22,166 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf', name='DejaVu Sans', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 0.33499999999999996
2025-07-14 16:43:22,166 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/ubuntu/Ubuntu-B.ttf', name='Ubuntu', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-07-14 16:43:22,166 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/dejavu/DejaVuSerif-Bold.ttf', name='DejaVu Serif', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-07-14 16:43:22,166 - matplotlib.font_manager - DEBUG - findfont: Matching sans\-serif:style=normal:variant=normal:weight=normal:stretch=normal:size=16.0 to DejaVu Sans ('/home/<USER>/MOS_spice/mosfet_spice_fitter/.venv/lib/python3.13/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf') with score of 0.050000.
2025-07-14 16:43:22,906 - main - INFO - Comparison plots saved to: output/plots/all_characteristics_comparison.png
2025-07-14 16:43:22,906 - main - INFO - Step 7: Generating comprehensive fitting report...
2025-07-14 16:43:22,916 - main - INFO - HTML report saved to: output/reports/fitting_report.html
2025-07-14 16:43:22,916 - main - INFO - JSON results saved to: output/reports/fitting_results.json
2025-07-14 16:43:22,916 - main - INFO - Step 8: Workflow completed successfully!
2025-07-14 16:43:22,916 - main - INFO - ============================================================
2025-07-14 16:43:22,916 - main - INFO - SUMMARY:
2025-07-14 16:43:22,917 - main - INFO -   Fitted parameters: 3
2025-07-14 16:43:22,917 - main - INFO -   Characteristics analyzed: 3
2025-07-14 16:43:22,917 - main - INFO -   Overall fitting quality (avg R²): 0.2899
2025-07-14 16:43:22,917 - main - INFO -   Output directory: output
2025-07-14 16:43:22,917 - main - INFO - 
Generated files:
2025-07-14 16:43:22,917 - main - INFO -   - Fitted SPICE model: output/models/fitted_model.lib
2025-07-14 16:43:22,917 - main - INFO -   - HTML report: output/reports/fitting_report.html
2025-07-14 16:43:22,917 - main - INFO -   - JSON results: output/reports/fitting_results.json
2025-07-14 16:43:22,918 - main - INFO -   - Comparison plots: output/plots/
2025-07-14 16:43:22,918 - main - INFO -   - Test circuits: output/circuits/
