simulator lang=pspice

* Generated SPICE Model for Power MOSFET
* Auto-generated by MOSFET Parameter Fitter

.SUBCKT SWA04K006 D G S

.PARAM param_Cgs_0=7.720000000007177e-11
.PARAM param_Cgs_1=-6.512941772401471e-09
.<PERSON><PERSON><PERSON> param_Cgs_2=-2.669745064818513e-12
.PARAM param_Cgs_3=1.025358350456349e-14
.PARAM param_Cgs_4=-4.126922856972589e-18
.PARAM param_Cgs_5=-6.230466856750315e-18
.PARAM param_Cgs_6=-1.454711490054403e-18
.PARAM param_Cgs_7=-3.669450009522969e-18
.PARAM param_Cgs_8=-1.907869866217173e-19
.PARA<PERSON> param_Cgs_9=1.168366883370537e-18
.PARA<PERSON> param_Cgs_10=2.443809083189881e-18
.PARAM param_Cds_0=1.685371282464444e-09
.PARAM param_Cds_1=-1.371679112903376e-09
.PARAM param_Cds_2=-1.783679886757480e-11
.PARAM param_Cds_3=-1.517489732228821e-14
.PARAM param_Cds_4=-4.685475775696247e-18
.PARAM param_Cds_5=1.018012067243505e-18
.PARAM param_Cds_6=-6.507645571440485e-18
.PARAM param_Cds_7=2.974723308878041e-18
.PARAM param_Cds_8=3.395216332195496e-18
.PARAM param_Cds_9=4.716874873085112e-19
.PARAM param_Cds_10=-1.038038509052780e-18
.PARAM param_Cgd_0=3.830000000000056e-11
.PARAM param_Cgd_1=-3.191563437054517e-09
.PARAM param_Cgd_2=-5.239043226644327e-12
.PARAM param_Cgd_3=-7.537671685420026e-15
.PARAM param_Cgd_4=-8.033325783357461e-18
.PARAM param_Cgd_5=4.332326028101662e-18
.PARAM param_Cgd_6=1.775387069498357e-18
.PARAM param_Cgd_7=-4.245002807934234e-19
.PARAM param_Cgd_8=3.792514609430804e-19
.PARAM param_Cgd_9=8.659945001690074e-20
.PARAM param_Cgd_10=2.630206930961389e-19

.FUNC Cgs(Vds) {param_Cgs_0 + param_Cgs_1 * Vds**1 + param_Cgs_2 * Vds**2 + param_Cgs_3 * Vds**3 + param_Cgs_4 * Vds**4 + param_Cgs_5 * Vds**5 + param_Cgs_6 * Vds**6 + param_Cgs_7 * Vds**7 + param_Cgs_8 * Vds**8 + param_Cgs_9 * Vds**9 + param_Cgs_10 * Vds**10}
.FUNC Cds(Vds) {param_Cds_0 + param_Cds_1 * Vds**1 + param_Cds_2 * Vds**2 + param_Cds_3 * Vds**3 + param_Cds_4 * Vds**4 + param_Cds_5 * Vds**5 + param_Cds_6 * Vds**6 + param_Cds_7 * Vds**7 + param_Cds_8 * Vds**8 + param_Cds_9 * Vds**9 + param_Cds_10 * Vds**10}
.FUNC Cgd(Vds) {param_Cgd_0 + param_Cgd_1 * Vds**1 + param_Cgd_2 * Vds**2 + param_Cgd_3 * Vds**3 + param_Cgd_4 * Vds**4 + param_Cgd_5 * Vds**5 + param_Cgd_6 * Vds**6 + param_Cgd_7 * Vds**7 + param_Cgd_8 * Vds**8 + param_Cgd_9 * Vds**9 + param_Cgd_10 * Vds**10}

LD D D_int_L 2.000000000000000e-09
RD_L_PAR D D_int_L 5.000000000000000e-05
RLD1 D_int_L D_int_MOS 6.000000000000000e-06
RD D_int_MOS D_int_MOS_internal 1.604688000000000e-03

LG G G_int_L 7.812926960751280e-10
RLG G G_int_L 1.963602715434390e+00
RG G_int_L G_int_MOS 2.500000000000000e+00

LS S S_int_L 4.000000000000000e-09
RS_L_PAR S S_int_L 5.000000000000000e-05
RLS1 S_int_L S_int_MOS 5.500000000000000e-04
RS S_int_MOS S_int_MOS_internal 1.604688000000000e-03

M1 D_int_MOS_internal G_int_MOS S_int_MOS_internal S_int_MOS_internal MINT

DBGS S_int_MOS_internal G_int_MOS DBGS
DBD S_int_MOS_internal D_int_MOS_internal DBD

C_gs S_int_MOS_internal G_int_MOS capacitor C = Cgs(V(D_int_MOS_internal, S_int_MOS_internal))
C_ds D_int_MOS_internal S_int_MOS_internal capacitor C = Cds(V(D_int_MOS_internal, S_int_MOS_internal))
C_gd D_int_MOS_internal G_int_MOS capacitor C = Cgd(V(D_int_MOS_internal, S_int_MOS_internal))

.ENDS SWA04K006

.MODEL MINT NMOS(Vto=2.514 Kp=92.6458973 Nfs=440000000000 Eta=1000 Level=3 L=0.0001 W=0.0001 Gamma=0 Phi=0.6 Is=1e-24 U0=600.0 Vmax=10000)

.MODEL DBD D(Bv=46.4658 Ibv=0.0002515978465 Rs=1e-06 Is=2.74564811268e-12 N=1 M=0.55 VJ=0.7 Fc=0.5 Cjo=8.7201532e-10 Tt=1.05648165e-08)
.MODEL DBGS D(Bv=37.8654 Ibv=8.6572e-07)