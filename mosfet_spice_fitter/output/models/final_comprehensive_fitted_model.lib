simulator lang=pspice

* Generated SPICE Model for Power MOSFET
* Auto-generated by MOSFET Parameter Fitter

.SUBCKT SWA04K006 D G S

.PARAM param_Cgs_0=1.005327866763338e-09
.PARAM param_Cgs_1=-1.596890817101428e-13
.PARA<PERSON> param_Cgs_2=3.061680514891450e-18
.PARAM param_Cgs_3=-5.469443730479219e-20
.PARAM param_Cgs_4=2.008078912573470e-20
.PARAM param_Cgs_5=2.800041660310378e-20
.PARAM param_Cgs_6=-2.475387129714411e-21
.PARAM param_Cgs_7=6.886724634245306e-21
.PARA<PERSON> param_Cgs_8=-1.825828437889262e-21
.PARAM param_Cgs_9=9.305686803392364e-22
.PARA<PERSON> param_Cgs_10=-1.588311591417648e-21
.PARA<PERSON> param_Cds_0=1.417207123117070e-10
.PARA<PERSON> param_Cds_1=-8.523140621373253e-13
.PARAM param_Cds_2=3.579758079014386e-16
.PARAM param_Cds_3=-3.338464184129344e-19
.PARAM param_Cds_4=1.088954010205202e-20
.PARAM param_Cds_5=-9.288832677083238e-22
.PARAM param_Cds_6=2.010311715858476e-20
.PARAM param_Cds_7=2.828095808504144e-23
.PARAM param_Cds_8=1.959456123892539e-21
.PARAM param_Cds_9=7.432413387442910e-22
.PARAM param_Cds_10=2.175707561745728e-23
.PARAM param_Cgd_0=3.198482729864349e-10
.PARAM param_Cgd_1=-8.796444234931167e-13
.PARAM param_Cgd_2=5.462021182922248e-16
.PARAM param_Cgd_3=-6.257667763579449e-23
.PARAM param_Cgd_4=-2.834517945396374e-20
.PARAM param_Cgd_5=-3.122696053044892e-21
.PARAM param_Cgd_6=2.269447405011108e-21
.PARAM param_Cgd_7=-1.051657460118881e-21
.PARAM param_Cgd_8=-1.031681971975281e-21
.PARAM param_Cgd_9=-5.715555887808704e-24
.PARAM param_Cgd_10=-5.556922148239101e-22

.FUNC Cgs(Vds) {param_Cgs_0 + param_Cgs_1 * Vds**1 + param_Cgs_2 * Vds**2 + param_Cgs_3 * Vds**3 + param_Cgs_4 * Vds**4 + param_Cgs_5 * Vds**5 + param_Cgs_6 * Vds**6 + param_Cgs_7 * Vds**7 + param_Cgs_8 * Vds**8 + param_Cgs_9 * Vds**9 + param_Cgs_10 * Vds**10}
.FUNC Cds(Vds) {param_Cds_0 + param_Cds_1 * Vds**1 + param_Cds_2 * Vds**2 + param_Cds_3 * Vds**3 + param_Cds_4 * Vds**4 + param_Cds_5 * Vds**5 + param_Cds_6 * Vds**6 + param_Cds_7 * Vds**7 + param_Cds_8 * Vds**8 + param_Cds_9 * Vds**9 + param_Cds_10 * Vds**10}
.FUNC Cgd(Vds) {param_Cgd_0 + param_Cgd_1 * Vds**1 + param_Cgd_2 * Vds**2 + param_Cgd_3 * Vds**3 + param_Cgd_4 * Vds**4 + param_Cgd_5 * Vds**5 + param_Cgd_6 * Vds**6 + param_Cgd_7 * Vds**7 + param_Cgd_8 * Vds**8 + param_Cgd_9 * Vds**9 + param_Cgd_10 * Vds**10}

LD D D_int_L 2.000000000000000e-09
RD_L_PAR D D_int_L 5.000000000000000e-05
RLD1 D_int_L D_int_MOS 6.000000000000000e-06
RD D_int_MOS D_int_MOS_internal 1.604688000000000e-03

LG G G_int_L 7.812926960751280e-10
RLG G G_int_L 1.963602715434390e+00
RG G_int_L G_int_MOS 2.500000000000000e+00

LS S S_int_L 4.000000000000000e-09
RS_L_PAR S S_int_L 5.000000000000000e-05
RLS1 S_int_L S_int_MOS 5.500000000000000e-04
RS S_int_MOS S_int_MOS_internal 1.604688000000000e-03

M1 D_int_MOS_internal G_int_MOS S_int_MOS_internal S_int_MOS_internal MINT

DBGS S_int_MOS_internal G_int_MOS DBGS
DBD S_int_MOS_internal D_int_MOS_internal DBD

C_gs S_int_MOS_internal G_int_MOS capacitor C = Cgs(V(D_int_MOS_internal, S_int_MOS_internal))
C_ds D_int_MOS_internal S_int_MOS_internal capacitor C = Cds(V(D_int_MOS_internal, S_int_MOS_internal))
C_gd D_int_MOS_internal G_int_MOS capacitor C = Cgd(V(D_int_MOS_internal, S_int_MOS_internal))

.ENDS SWA04K006

.MODEL MINT NMOS(Vto=2.514 Kp=92.6458973 Nfs=440000000000 Eta=1000 Level=3 L=0.0001 W=0.0001 Gamma=0 Phi=0.6 Is=1e-24 U0=600 Vmax=10000)

.MODEL DBD D(Bv=46.4658 Ibv=0.0002515978465 Rs=1e-06 Is=2.74564811268e-12 N=1 M=0.55 VJ=0.7 Fc=0.5 Cjo=8.7201532e-10 Tt=1.05648165e-08)
.MODEL DBGS D(Bv=37.8654 Ibv=8.6572e-07)