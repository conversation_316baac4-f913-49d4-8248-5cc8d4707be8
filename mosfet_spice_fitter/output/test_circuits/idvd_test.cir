* IdVd Test Circuit - Drain Current vs Drain Voltage
* Fixed Vg = 5.0V, Sweep Vd from 0.0V to 10.0V

.include output/test_integration_model.lib

* Test circuit
VGS gate 0 DC 5.0
VDS drain 0 DC 0
VSS source 0 DC 0

* Device under test
X1 drain gate source SWA04K006

* Current measurement through drain
.probe I(VDS)

* DC sweep analysis
.dc VDS 0.0 10.0 0.1
.print dc v(drain) I(VDS)

.control
run
print all
.endc

.end