"""
Main MOSFET SPICE Parameter Fitter

This is the main entry point for the MOSFET SPICE parameter fitting workflow.
It integrates all components to provide a complete parameter fitting solution.
"""

import argparse
import sys
from pathlib import Path
import logging
import numpy as np

from src.mosfet_fitter.data_parsers.electrical_data_parser import ElectricalDataParser
from src.mosfet_fitter.data_parsers.spice_lib_parser import SpiceLibParser
from src.mosfet_fitter.model_templates.spice_template import SpiceModelTemplate
from src.mosfet_fitter.spice_interface.ngspice_interface import NgspiceInterface
from src.mosfet_fitter.test_circuits.circuit_generator import CircuitGenerator
from src.mosfet_fitter.optimization.parameter_fitter import ParameterFitter
from src.mosfet_fitter.output_generation.report_generator import ReportGenerator


def setup_logging(verbose: bool = False):
    """Set up logging configuration."""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('output/fitting.log')
        ]
    )


def check_prerequisites():
    """Check if all prerequisites are available."""
    logger = logging.getLogger('main')

    # Check if ngspice is available
    ngspice = NgspiceInterface()
    if not ngspice.test_ngspice_availability():
        logger.error("ngspice is not available. Please install ngspice and ensure it's in your PATH.")
        return False

    logger.info("ngspice is available and working")

    # Check if raw data directory exists
    if not Path("raw_data").exists():
        logger.error("raw_data directory not found. Please ensure measurement data is available.")
        return False

    logger.info("Raw data directory found")
    return True


def run_complete_fitting_workflow(data_dir: str = "raw_data",
                                 output_dir: str = "output",
                                 parameter_subset: list = None,
                                 verbose: bool = False):
    """
    Run the complete MOSFET parameter fitting workflow.

    Args:
        data_dir: Directory containing measurement data
        output_dir: Directory for output files
        parameter_subset: List of specific parameters to fit (None for default subset)
        verbose: Enable verbose logging
    """
    setup_logging(verbose)
    logger = logging.getLogger('main')

    logger.info("Starting MOSFET SPICE Parameter Fitting Workflow")
    logger.info("=" * 60)

    # Check prerequisites
    if not check_prerequisites():
        logger.error("Prerequisites check failed. Exiting.")
        return False

    try:
        # Step 1: Initialize components
        logger.info("Step 1: Initializing components...")

        data_parser = ElectricalDataParser(data_dir)
        lib_parser = SpiceLibParser()
        model_template = SpiceModelTemplate()
        fitter = ParameterFitter(data_dir, f"{output_dir}/models/fitted_model.lib")
        report_generator = ReportGenerator(output_dir)

        logger.info("All components initialized successfully")

        # Step 2: Load and analyze data
        logger.info("Step 2: Loading and analyzing measurement data...")

        electrical_data = data_parser.get_all_electrical_data()
        logger.info(f"Loaded electrical data for: {list(electrical_data.keys())}")

        spice_data = lib_parser.parse_library()
        logger.info(f"Analyzed SPICE library: {list(spice_data['subcircuits'].keys())}")

        # Step 3: Generate test circuits
        logger.info("Step 3: Generating test circuits...")

        circuit_generator = CircuitGenerator(f"{output_dir}/models/fitted_model.lib")
        circuit_generator.save_circuits_to_files(f"{output_dir}/circuits")

        logger.info("Test circuits generated and saved")

        # Step 4: Run parameter fitting
        logger.info("Step 4: Running parameter fitting...")
        logger.info("This may take several minutes depending on the number of parameters...")

        fitting_result = fitter.fit_parameters(parameter_subset)

        if fitting_result.success:
            logger.info(f"Parameter fitting completed successfully!")
            logger.info(f"Final cost: {fitting_result.final_cost:.6e}")
            logger.info(f"Iterations: {fitting_result.iterations}")
            logger.info(f"Time: {fitting_result.fitting_time:.2f} seconds")
        else:
            logger.error("Parameter fitting failed!")
            logger.error(f"Error info: {fitting_result.convergence_info}")
            return False

        # Step 5: Generate fitted model
        logger.info("Step 5: Generating fitted SPICE model...")

        fitter.save_fitted_model(fitting_result, f"{output_dir}/models/fitted_model.lib")
        logger.info("Fitted SPICE model saved")

        # Step 6: Generate comparison data and plots
        logger.info("Step 6: Generating comparison plots and reports...")

        comparisons = report_generator.generate_comparison_data(fitter, fitting_result)
        plot_path = report_generator.create_comparison_plots(comparisons)

        logger.info(f"Comparison plots saved to: {plot_path}")

        # Step 7: Generate comprehensive report
        logger.info("Step 7: Generating comprehensive fitting report...")

        report_path = report_generator.generate_fitting_report(fitter, fitting_result, comparisons)
        json_path = report_generator.save_results_json(fitting_result, comparisons)

        logger.info(f"HTML report saved to: {report_path}")
        logger.info(f"JSON results saved to: {json_path}")

        # Step 8: Summary
        logger.info("Step 8: Workflow completed successfully!")
        logger.info("=" * 60)
        logger.info("SUMMARY:")
        logger.info(f"  Fitted parameters: {len(fitting_result.fitted_parameters)}")
        logger.info(f"  Characteristics analyzed: {len(comparisons)}")
        logger.info(f"  Overall fitting quality (avg R²): {np.mean([c.r_squared for c in comparisons]):.4f}")
        logger.info(f"  Output directory: {output_dir}")

        logger.info("\nGenerated files:")
        logger.info(f"  - Fitted SPICE model: {output_dir}/models/fitted_model.lib")
        logger.info(f"  - HTML report: {report_path}")
        logger.info(f"  - JSON results: {json_path}")
        logger.info(f"  - Comparison plots: {output_dir}/plots/")
        logger.info(f"  - Test circuits: {output_dir}/circuits/")

        return True

    except Exception as e:
        logger.error(f"Workflow failed with error: {e}")
        logger.exception("Full traceback:")
        return False


def main():
    """Main entry point with command line interface."""
    parser = argparse.ArgumentParser(
        description="MOSFET SPICE Parameter Fitter",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                           # Run with default settings
  python main.py --verbose                 # Run with verbose output
  python main.py --data-dir custom_data    # Use custom data directory
  python main.py --output-dir results      # Use custom output directory
  python main.py --params Vto Kp U0       # Fit only specific parameters
        """
    )

    parser.add_argument(
        '--data-dir',
        default='raw_data',
        help='Directory containing measurement data (default: raw_data)'
    )

    parser.add_argument(
        '--output-dir',
        default='output',
        help='Directory for output files (default: output)'
    )

    parser.add_argument(
        '--params',
        nargs='*',
        help='Specific parameters to fit (default: automatic selection)'
    )

    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )

    parser.add_argument(
        '--check-only',
        action='store_true',
        help='Only check prerequisites and exit'
    )

    args = parser.parse_args()

    # Create output directory
    Path(args.output_dir).mkdir(parents=True, exist_ok=True)

    if args.check_only:
        setup_logging(args.verbose)
        success = check_prerequisites()
        sys.exit(0 if success else 1)

    # Run the complete workflow
    success = run_complete_fitting_workflow(
        data_dir=args.data_dir,
        output_dir=args.output_dir,
        parameter_subset=args.params,
        verbose=args.verbose
    )

    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
