#!/usr/bin/env python3
"""
Simple Comprehensive MOSFET Parameter Fitting Script

This script implements a more stable approach:
1. Use linear space for capacitance fitting first
2. Include all characteristics: IdVg, IdVd, BVDSS, and capacitances
3. Log-scale plotting for better visualization
4. Conservative parameter bounds to avoid numerical issues
"""

import sys
from pathlib import Path
import numpy as np

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.mosfet_fitter.optimization.parameter_fitter import ParameterFitter
from src.mosfet_fitter.output_generation.report_generator import ReportGenerator


def run_simple_comprehensive_fitting():
    """Run comprehensive parameter fitting with all characteristics."""
    
    print("🚀 Starting Simple Comprehensive MOSFET Parameter Fitting")
    print("   - All characteristics: IdVg, IdVd, BVDSS, Cgs, Cds, Cgd")
    print("   - Log-scale plotting for better visualization")
    print("   - Conservative approach to avoid numerical issues")
    print("=" * 70)
    
    # Initialize components
    fitter = ParameterFitter("raw_data", "output/models/simple_comprehensive_fitted_model.lib")
    report_generator = ReportGenerator("output")
    
    print(f"📊 Loaded measurement data: {list(fitter.measurement_data.keys())}")
    
    # Check which characteristics have data
    available_chars = []
    for char_name, measurement in fitter.measurement_data.items():
        if measurement is not None and len(measurement.x_data) > 0:
            available_chars.append(char_name)
            print(f"   ✅ {char_name}: {len(measurement.x_data)} data points")
        else:
            print(f"   ❌ {char_name}: No data or empty")
    
    if not available_chars:
        print("❌ No measurement data available for fitting!")
        return False
    
    # Get all fittable parameters
    all_fittable = fitter.model_template.get_all_fittable_parameters()
    print(f"\n📋 Available parameters: {len(all_fittable)}")
    
    # Stage 1: Fit capacitance parameters (use only first few orders)
    print(f"\n🔧 Stage 1: Capacitance Parameter Fitting")
    print("-" * 50)
    
    capacitance_params = []
    for cap_type in ['Cgs', 'Cds', 'Cgd']:
        if cap_type in available_chars:
            # Use only first 3 orders (0, 1, 2) to avoid overfitting
            for order in range(3):
                param_name = f'param_{cap_type}_{order}'
                if param_name in all_fittable:
                    capacitance_params.append(param_name)
    
    print(f"   Fitting {len(capacitance_params)} capacitance parameters")
    print(f"   Parameters: {capacitance_params}")
    
    cap_result = None
    if capacitance_params:
        try:
            cap_result = fitter.fit_parameters(capacitance_params)
            
            if cap_result.success:
                print(f"✅ Capacitance fitting successful!")
                print(f"   Final cost: {cap_result.final_cost:.6e}")
                print(f"   Iterations: {cap_result.iterations}")
                print(f"   Time: {cap_result.fitting_time:.2f} seconds")
                
                # Update parameters with fitted values
                for name, value in cap_result.fitted_parameters.items():
                    if name in all_fittable:
                        all_fittable[name].initial_value = value
            else:
                print(f"❌ Capacitance fitting failed: {cap_result.convergence_info}")
                cap_result = None
        except Exception as e:
            print(f"❌ Capacitance fitting error: {e}")
            cap_result = None
    else:
        print("⚠️  No capacitance parameters to fit")
    
    # Stage 2: Fit MOSFET parameters for current characteristics
    print(f"\n🔧 Stage 2: MOSFET Parameter Fitting")
    print("-" * 40)
    
    mosfet_params = []
    if any(char in available_chars for char in ['IdVg', 'IdVd']):
        mosfet_params = [
            'Vto',      # Threshold voltage
            'Kp',       # Transconductance parameter
            'U0',       # Surface mobility
        ]
        mosfet_params = [p for p in mosfet_params if p in all_fittable]
    
    mosfet_result = None
    if mosfet_params:
        print(f"   Fitting {len(mosfet_params)} MOSFET parameters")
        print(f"   Parameters: {mosfet_params}")
        
        try:
            mosfet_result = fitter.fit_parameters(mosfet_params)
            
            if mosfet_result.success:
                print(f"✅ MOSFET parameter fitting successful!")
                print(f"   Final cost: {mosfet_result.final_cost:.6e}")
                print(f"   Iterations: {mosfet_result.iterations}")
                print(f"   Time: {mosfet_result.fitting_time:.2f} seconds")
                
                # Update parameters
                for name, value in mosfet_result.fitted_parameters.items():
                    if name in all_fittable:
                        all_fittable[name].initial_value = value
            else:
                print(f"❌ MOSFET parameter fitting failed: {mosfet_result.convergence_info}")
                mosfet_result = None
        except Exception as e:
            print(f"❌ MOSFET parameter fitting error: {e}")
            mosfet_result = None
    else:
        print("⚠️  No MOSFET parameters to fit")
    
    # Stage 3: Fit diode parameters for breakdown characteristics
    print(f"\n🔧 Stage 3: Diode Parameter Fitting")
    print("-" * 40)
    
    diode_params = []
    if 'BVDSS' in available_chars:
        diode_params = [
            'DBD_Bv',   # Breakdown voltage
        ]
        diode_params = [p for p in diode_params if p in all_fittable]
    
    diode_result = None
    if diode_params:
        print(f"   Fitting {len(diode_params)} diode parameters")
        print(f"   Parameters: {diode_params}")
        
        try:
            diode_result = fitter.fit_parameters(diode_params)
            
            if diode_result.success:
                print(f"✅ Diode parameter fitting successful!")
                print(f"   Final cost: {diode_result.final_cost:.6e}")
                print(f"   Iterations: {diode_result.iterations}")
                print(f"   Time: {diode_result.fitting_time:.2f} seconds")
                
                # Update parameters
                for name, value in diode_result.fitted_parameters.items():
                    if name in all_fittable:
                        all_fittable[name].initial_value = value
            else:
                print(f"❌ Diode parameter fitting failed: {diode_result.convergence_info}")
                diode_result = None
        except Exception as e:
            print(f"❌ Diode parameter fitting error: {e}")
            diode_result = None
    else:
        print("⚠️  No diode parameters to fit")
    
    # Combine all fitted parameters
    all_fitted_params = {}
    total_iterations = 0
    total_time = 0.0
    
    successful_results = []
    for result in [cap_result, mosfet_result, diode_result]:
        if result and result.success:
            all_fitted_params.update(result.fitted_parameters)
            total_iterations += result.iterations
            total_time += result.fitting_time
            successful_results.append(result)
    
    if not all_fitted_params:
        print("❌ No successful fitting results!")
        return False
    
    # Create comprehensive result using the last successful result as template
    from src.mosfet_fitter.optimization.parameter_fitter import FittingResult
    
    last_result = successful_results[-1]
    
    # Get initial parameters
    initial_params = {}
    for name in all_fitted_params.keys():
        if name in all_fittable:
            initial_params[name] = all_fittable[name].initial_value
    
    comprehensive_result = FittingResult(
        success=True,
        fitted_parameters=all_fitted_params,
        initial_parameters=initial_params,
        final_cost=last_result.final_cost,
        iterations=total_iterations,
        convergence_info=last_result.convergence_info,
        fitting_time=total_time
    )
    
    # Generate results
    print(f"\n📊 Generating Comprehensive Results")
    print("-" * 40)
    
    try:
        # Save fitted model
        fitter.save_fitted_model(comprehensive_result, "output/models/simple_comprehensive_fitted_model.lib")
        print("✅ Comprehensive fitted model saved")
        
        # Generate comparison data and plots
        comparisons = report_generator.generate_comparison_data(fitter, comprehensive_result)
        print(f"✅ Generated {len(comparisons)} characteristic comparisons")
        
        # List what comparisons were generated
        for comp in comparisons:
            print(f"   - {comp.characteristic}: R² = {comp.r_squared:.4f}")
        
        plot_path = report_generator.create_comparison_plots(comparisons, save_individual=True)
        print(f"✅ Comparison plots saved: {plot_path}")
        
        # Generate comprehensive report
        report_path = report_generator.generate_fitting_report(fitter, comprehensive_result, comparisons)
        json_path = report_generator.save_results_json(comprehensive_result, comparisons)
        print(f"✅ HTML report saved: {report_path}")
        print(f"✅ JSON results saved: {json_path}")
        
    except Exception as e:
        print(f"❌ Error generating results: {e}")
        return False
    
    # Final summary
    print(f"\n🎉 SIMPLE COMPREHENSIVE FITTING COMPLETED!")
    print("=" * 60)
    print(f"📊 Total fitted parameters: {len(all_fitted_params)}")
    print(f"📊 Characteristics analyzed: {len(comparisons)}")
    
    if comparisons:
        avg_r2 = np.mean([c.r_squared for c in comparisons])
        print(f"📊 Overall fitting quality (avg R²): {avg_r2:.4f}")
        
        print(f"\n📈 Individual Characteristic Quality:")
        for comp in comparisons:
            quality = "🟢 Excellent" if comp.r_squared > 0.99 else ("🟡 Good" if comp.r_squared > 0.95 else ("🟠 Fair" if comp.r_squared > 0.8 else "🔴 Poor"))
            print(f"   {comp.characteristic:<8}: R² = {comp.r_squared:.4f}, RMSE = {comp.rmse:.3e} {quality}")
    
    print(f"\n📁 Output files:")
    print(f"   - Comprehensive model: output/models/simple_comprehensive_fitted_model.lib")
    print(f"   - HTML report: {report_path}")
    print(f"   - JSON results: {json_path}")
    print(f"   - Plots: output/plots/ (with log-scale Y-axis)")
    
    return True


if __name__ == "__main__":
    success = run_simple_comprehensive_fitting()
    sys.exit(0 if success else 1)
