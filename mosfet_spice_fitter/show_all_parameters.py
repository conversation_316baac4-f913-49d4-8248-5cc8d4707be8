#!/usr/bin/env python3
"""
Display All MOSFET Parameters (Static and Dynamic)

This script shows both fitted and non-fitted parameters with their current values,
including static MOSFET parameters like Vto, Kp, U0, etc.
"""

import sys
from pathlib import Path
import json

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.mosfet_fitter.optimization.parameter_fitter import ParameterFitter


def show_all_parameters():
    """Display all MOSFET parameters with their current values."""
    
    print("📋 COMPLETE MOSFET PARAMETER STATUS")
    print("=" * 70)
    
    # Initialize fitter to get all parameters
    fitter = ParameterFitter("raw_data", "output/models/temp_model.lib")
    all_params = fitter.model_template.get_all_fittable_parameters()
    
    # Load fitting results if available
    fitted_params = {}
    results_file = Path("output/reports/fitting_results.json")
    if results_file.exists():
        with open(results_file, 'r') as f:
            results = json.load(f)
            fitted_params = results.get('fitted_parameters', {})
    
    print(f"📊 Total Parameters: {len(all_params)}")
    print(f"🔧 Fitted Parameters: {len(fitted_params)}")
    print(f"📌 Default Parameters: {len(all_params) - len(fitted_params)}")
    
    # Categorize parameters
    categories = {
        'Static MOSFET Parameters': [],
        'Capacitance Parameters': [],
        'Diode Parameters': [],
        'Parasitic Parameters': [],
        'Other Parameters': []
    }
    
    # Classify parameters
    for name, param in all_params.items():
        if name.startswith('param_C'):
            categories['Capacitance Parameters'].append((name, param))
        elif name in ['Vto', 'Kp', 'U0', 'Gamma', 'Phi', 'Lambda', 'Theta', 'Eta', 'Kappa']:
            categories['Static MOSFET Parameters'].append((name, param))
        elif name.startswith('DBD_') or name.startswith('DBS_'):
            categories['Diode Parameters'].append((name, param))
        elif name in ['LD', 'RD', 'LG', 'RG', 'LS', 'RS']:
            categories['Parasitic Parameters'].append((name, param))
        else:
            categories['Other Parameters'].append((name, param))
    
    # Display parameters by category
    for category, params in categories.items():
        if not params:
            continue
            
        print(f"\n🔹 {category.upper()}")
        print("-" * 60)
        print(f"{'Parameter':<20} {'Current Value':<15} {'Status':<12} {'Unit':<8} {'Description'}")
        print("-" * 60)
        
        for name, param in sorted(params):
            # Get current value (fitted or default)
            if name in fitted_params:
                current_value = fitted_params[name]
                status = "🟢 FITTED"
            else:
                current_value = param.initial_value
                status = "📌 DEFAULT"
            
            # Format value
            if abs(current_value) < 1e-3 or abs(current_value) > 1e3:
                value_str = f"{current_value:.3e}"
            else:
                value_str = f"{current_value:.6f}"
            
            unit = getattr(param, 'unit', '')
            description = getattr(param, 'description', '')[:30] + "..." if len(getattr(param, 'description', '')) > 30 else getattr(param, 'description', '')
            
            print(f"{name:<20} {value_str:<15} {status:<12} {unit:<8} {description}")
    
    # Show parameter changes for fitted parameters
    if fitted_params and results_file.exists():
        print(f"\n📈 PARAMETER CHANGES (Fitted Parameters Only)")
        print("-" * 70)
        print(f"{'Parameter':<20} {'Initial Value':<15} {'Fitted Value':<15} {'Change (%)':<12}")
        print("-" * 70)
        
        initial_params = results.get('initial_parameters', {})
        
        for name in sorted(fitted_params.keys()):
            fitted_val = fitted_params[name]
            initial_val = initial_params.get(name, fitted_val)
            
            if initial_val != 0:
                change = ((fitted_val - initial_val) / initial_val) * 100
            else:
                change = float('inf') if fitted_val != 0 else 0
            
            initial_str = f"{initial_val:.3e}"
            fitted_str = f"{fitted_val:.3e}"
            
            if abs(change) < 1000:
                change_str = f"{change:+.1f}%"
            else:
                change_str = "Large"
            
            print(f"{name:<20} {initial_str:<15} {fitted_str:<15} {change_str:<12}")
    
    # Show fitting quality summary
    if results_file.exists() and 'characteristic_metrics' in results:
        print(f"\n📊 FITTING QUALITY SUMMARY")
        print("-" * 40)
        
        metrics = results['characteristic_metrics']
        for char_name, char_metrics in metrics.items():
            r2 = char_metrics['r_squared']
            rmse = char_metrics['rmse']
            points = char_metrics['data_points']
            
            if r2 > 0.99:
                quality = "🟢 Excellent"
            elif r2 > 0.95:
                quality = "🟡 Good"
            elif r2 > 0.8:
                quality = "🟠 Fair"
            else:
                quality = "🔴 Poor"
            
            print(f"{char_name:<8}: R² = {r2:8.4f}, RMSE = {rmse:.3e}, Points = {points:3d} {quality}")
    
    # Show recommendations
    print(f"\n💡 PARAMETER FITTING RECOMMENDATIONS")
    print("-" * 50)
    
    unfitted_static = [name for name, param in categories['Static MOSFET Parameters'] if name not in fitted_params]
    unfitted_diode = [name for name, param in categories['Diode Parameters'] if name not in fitted_params]
    
    if unfitted_static:
        print(f"🔧 Consider fitting static MOSFET parameters:")
        print(f"   {', '.join(unfitted_static)}")
        print(f"   These affect IdVg and IdVd characteristics")
    
    if unfitted_diode:
        print(f"🔧 Consider fitting diode parameters:")
        print(f"   {', '.join(unfitted_diode)}")
        print(f"   These affect BVDSS breakdown characteristics")
    
    # Show current model file location
    model_files = list(Path("output/models").glob("*.lib"))
    if model_files:
        print(f"\n📁 CURRENT MODEL FILES:")
        print("-" * 30)
        for model_file in sorted(model_files):
            print(f"   📄 {model_file}")
    
    print(f"\n📋 PARAMETER ACCESS LOCATIONS:")
    print("-" * 40)
    print(f"📄 JSON Results: output/reports/fitting_results.json")
    print(f"📄 HTML Report: output/reports/fitting_report.html")
    print(f"📄 SPICE Model: output/models/final_comprehensive_fitted_model.lib")
    print(f"🔧 This Script: python show_all_parameters.py")


if __name__ == "__main__":
    show_all_parameters()
