#!/usr/bin/env python3
"""
Integration Test Script

This script tests the integration of all components without running
the full parameter fitting process (which can be time-consuming).
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.mosfet_fitter.data_parsers.electrical_data_parser import ElectricalDataParser
from src.mosfet_fitter.data_parsers.spice_lib_parser import SpiceLibParser
from src.mosfet_fitter.model_templates.spice_template import SpiceModelTemplate
from src.mosfet_fitter.spice_interface.ngspice_interface import NgspiceInterface
from src.mosfet_fitter.test_circuits.circuit_generator import CircuitGenerator
from src.mosfet_fitter.optimization.parameter_fitter import ParameterFitter
from src.mosfet_fitter.output_generation.report_generator import ReportGenerator


def test_data_parsers():
    """Test data parsing components."""
    print("Testing data parsers...")
    
    # Test electrical data parser
    try:
        electrical_parser = ElectricalDataParser("raw_data")
        electrical_data = electrical_parser.get_all_electrical_data()
        print(f"✓ Electrical data parser: loaded {len(electrical_data)} data types")
        
        for data_type, data in electrical_data.items():
            if data:
                print(f"  - {data_type}: OK")
            else:
                print(f"  - {data_type}: No data")
    except Exception as e:
        print(f"✗ Electrical data parser failed: {e}")
        return False
    
    # Test SPICE library parser
    try:
        spice_parser = SpiceLibParser("raw_data/SWA04K006_for_spectre.lib")
        spice_data = spice_parser.parse_library()
        print(f"✓ SPICE library parser: found {len(spice_data['subcircuits'])} subcircuits")
    except Exception as e:
        print(f"✗ SPICE library parser failed: {e}")
        return False
    
    return True


def test_model_template():
    """Test SPICE model template system."""
    print("\nTesting model template system...")
    
    try:
        template = SpiceModelTemplate()
        fittable_params = template.get_all_fittable_parameters()
        print(f"✓ Model template: {len(fittable_params)} fittable parameters")
        
        # Test model generation
        param_values = {name: param.initial_value for name, param in fittable_params.items()}
        model_content = template.generate_spice_model(param_values)
        print(f"✓ Model generation: {len(model_content)} characters")
        
        # Save test model
        template.save_model_to_file(param_values, "output/test_integration_model.lib")
        print("✓ Model saved to output/test_integration_model.lib")
        
    except Exception as e:
        print(f"✗ Model template failed: {e}")
        return False
    
    return True


def test_ngspice_interface():
    """Test NGSPICE interface."""
    print("\nTesting NGSPICE interface...")
    
    try:
        ngspice = NgspiceInterface()
        
        # Test availability
        if not ngspice.test_ngspice_availability():
            print("✗ NGSPICE not available")
            return False
        
        print("✓ NGSPICE is available")
        
        # Test simple simulation
        test_netlist = """
* Simple test circuit
V1 vin 0 DC 5
R1 vin vout 1k
R2 vout 0 1k
"""
        
        result = ngspice.run_dc_sweep(
            test_netlist,
            sweep_var="V1",
            start=0,
            stop=10,
            step=1,
            output_vars=["v(vin)", "v(vout)"]
        )
        
        if result.success:
            print(f"✓ DC sweep simulation: {len(result.data)} variables")
        else:
            print(f"✗ DC sweep simulation failed: {result.error_message}")
            return False
        
    except Exception as e:
        print(f"✗ NGSPICE interface failed: {e}")
        return False
    
    return True


def test_circuit_generator():
    """Test circuit generation."""
    print("\nTesting circuit generator...")
    
    try:
        generator = CircuitGenerator("output/test_integration_model.lib")
        
        # Test individual circuit generation
        circuits = generator.generate_all_circuits()
        print(f"✓ Circuit generation: {len(circuits)} circuits generated")
        
        for name, circuit in circuits.items():
            lines = len(circuit.split('\n'))
            print(f"  - {name}: {lines} lines")
        
        # Save circuits
        generator.save_circuits_to_files("output/test_circuits")
        print("✓ Circuits saved to output/test_circuits")
        
    except Exception as e:
        print(f"✗ Circuit generator failed: {e}")
        return False
    
    return True


def test_parameter_fitter_init():
    """Test parameter fitter initialization (without actual fitting)."""
    print("\nTesting parameter fitter initialization...")
    
    try:
        fitter = ParameterFitter("raw_data", "output/test_fitted_model.lib")
        print(f"✓ Parameter fitter initialized")
        print(f"  - Measurement data: {list(fitter.measurement_data.keys())}")
        
        # Test measurement data loading
        total_points = sum(len(data.y_data) for data in fitter.measurement_data.values())
        print(f"  - Total measurement points: {total_points}")
        
    except Exception as e:
        print(f"✗ Parameter fitter initialization failed: {e}")
        return False
    
    return True


def test_report_generator():
    """Test report generator."""
    print("\nTesting report generator...")
    
    try:
        report_gen = ReportGenerator("output")
        print("✓ Report generator initialized")
        
        # Test directory creation
        expected_dirs = ["plots", "reports", "models"]
        for dir_name in expected_dirs:
            dir_path = Path("output") / dir_name
            if dir_path.exists():
                print(f"  - {dir_name} directory: OK")
            else:
                print(f"  - {dir_name} directory: Missing")
        
    except Exception as e:
        print(f"✗ Report generator failed: {e}")
        return False
    
    return True


def main():
    """Run integration tests."""
    print("MOSFET SPICE Fitter - Integration Test")
    print("=" * 50)
    
    # Create output directory
    Path("output").mkdir(exist_ok=True)
    
    tests = [
        test_data_parsers,
        test_model_template,
        test_ngspice_interface,
        test_circuit_generator,
        test_parameter_fitter_init,
        test_report_generator,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print("Test failed!")
        except Exception as e:
            print(f"Test crashed: {e}")
    
    print("\n" + "=" * 50)
    print(f"Integration Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All integration tests passed!")
        print("\nYou can now run the full parameter fitting with:")
        print("  uv run python main.py")
        return True
    else:
        print("✗ Some integration tests failed!")
        print("Please fix the issues before running the full workflow.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
